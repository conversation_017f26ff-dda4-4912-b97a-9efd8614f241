# SmartCompass 前端项目

SmartCompass 是一个基于 Vue 3 + Element Plus 的智能金融分析平台前端应用。

## 项目特性

- 🚀 **Vue 3** - 使用最新的 Vue 3 Composition API
- 🎨 **Element Plus** - 现代化的 UI 组件库
- 🛣️ **Vue Router 4** - 官方路由管理器
- 📦 **Pinia** - 新一代状态管理
- 🌍 **Vue I18n** - 国际化支持
- 📱 **响应式设计** - 支持移动端和桌面端
- 🎯 **TypeScript Ready** - 支持 TypeScript（可选）

## 页面结构

### 主要页面
- **首页 (/)** - 搜索股票、财经早报、全球市场行情
- **股票分析 (/analysis/:symbol)** - AI 分析结果、技术分析、大师观点
- **我的账户 (/my)** - 用户信息、订阅状态、使用统计
- **登录注册 (/auth)** - 用户认证，支持多种登录方式
- **博客 (/blog)** - 投资洞察和市场分析文章
- **定价 (/pricing)** - 订阅计划和价格信息
- **论坛 (/forum)** - 投资者社区交流

### 组件结构
```
src/
├── components/          # 可复用组件
│   ├── Layout/         # 布局组件
│   │   ├── Header.vue  # 头部导航
│   │   └── Footer.vue  # 页脚
│   └── Charts/         # 图表组件
│       └── MiniChart.vue
├── views/              # 页面组件
├── router/             # 路由配置
├── stores/             # 状态管理
├── api/                # API 接口
├── utils/              # 工具函数
└── styles/             # 全局样式
```

## 安装和运行

### 环境要求
- Node.js >= 16.0.0
- npm >= 8.0.0

### 安装依赖
```bash
npm install
```

### 开发环境运行
```bash
npm run serve
```

### 生产环境构建
```bash
npm run build
```

### 代码检查
```bash
npm run lint
```

## 技术栈

- **前端框架**: Vue 3
- **UI 组件库**: Element Plus
- **路由管理**: Vue Router 4
- **状态管理**: Pinia
- **HTTP 客户端**: Axios
- **图表库**: ECharts + Vue-ECharts
- **国际化**: Vue I18n
- **构建工具**: Vue CLI

## 功能特性

### 🔐 用户认证
- 邮箱/手机号/用户名登录
- 第三方登录（Google、GitHub、微信等）
- 用户注册和资料管理

### 📊 股票分析
- AI 智能分析和推荐
- 技术指标分析
- 大师观点解读
- 实时价格数据

### 💰 订阅管理
- 多种订阅计划
- 使用次数统计
- 自动续费管理

### 🌐 国际化
- 中文/英文切换
- 响应式设计
- 移动端适配

## 开发指南

### 添加新页面
1. 在 `src/views/` 创建页面组件
2. 在 `src/router/index.js` 添加路由配置
3. 在导航组件中添加菜单项

### 添加新 API
1. 在 `src/api/index.js` 添加 API 方法
2. 在组件中导入并使用

### 状态管理
使用 Pinia 进行状态管理，主要的 store：
- `useUserStore` - 用户信息和认证状态

### 样式规范
- 使用 CSS 变量定义主题色彩
- 响应式设计优先
- 组件样式使用 scoped

## 部署

### 环境变量
创建 `.env.production` 文件：
```
VUE_APP_API_BASE_URL=https://api.smartcompass.com
```

### 构建和部署
```bash
npm run build
```

构建产物在 `dist/` 目录中，可以部署到任何静态文件服务器。

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License
