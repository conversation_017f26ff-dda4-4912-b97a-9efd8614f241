{"name": "smart-composs-web", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"core-js": "^3.8.3", "vue": "^3.2.13", "element-plus": "^2.4.4", "vue-router": "^4.2.5", "pinia": "^2.1.7", "vue-i18n": "^9.8.0", "axios": "^1.6.2", "echarts": "^5.4.3", "vue-echarts": "^6.6.1", "@element-plus/icons-vue": "^2.3.1"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-service": "~5.0.0", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/vue3-essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {"vue/multi-word-component-names": "off", "no-unused-vars": "warn"}, "globals": {"defineProps": "readonly", "defineEmits": "readonly", "defineExpose": "readonly", "withDefaults": "readonly"}}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}