import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import { createI18n } from 'vue-i18n'
import './styles/global.css'

console.log('Starting SmartCompass application...')

// 国际化配置
const messages = {
  zh: {
    nav: {
      home: '首页',
      my: '我的账户',
      blog: '博客',
      pricing: '定价',
      forum: '论坛',
      login: '登录'
    },
    home: {
      search_placeholder: '请输入股票代码（如：AAPL, 00700.HK）',
      analyze_btn: '分析',
      financial_news: '财经早报',
      global_markets: '全球关键市场行情',
      guest: '游客'
    }
  },
  en: {
    nav: {
      home: 'Home',
      my: 'My Account',
      blog: 'Blog',
      pricing: 'Pricing',
      forum: 'Forum',
      login: 'Login'
    },
    home: {
      search_placeholder: 'Enter stock code (e.g., AAPL, 00700.HK)',
      analyze_btn: 'Analyze',
      financial_news: 'Financial News',
      global_markets: 'Global Market Index',
      guest: 'Guest'
    }
  }
}

const i18n = createI18n({
  locale: 'zh',
  fallbackLocale: 'en',
  messages
})

const app = createApp(App)

// 设置Vue 3兼容性配置
app.config.compilerOptions.isCustomElement = (tag) => {
  return tag.startsWith('el-')
}

// 全局配置
app.config.globalProperties.$ELEMENT = { size: 'default' }

const pinia = createPinia()

// 注册所有图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

app.use(router)
app.use(pinia)
app.use(ElementPlus)
app.use(i18n)
app.mount('#app')
