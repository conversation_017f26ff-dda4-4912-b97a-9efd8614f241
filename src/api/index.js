import request from '@/utils/request'

// 认证相关 API
export const authAPI = {
  // 登录
  login: (credentials) => request.post('/api/auth/login', credentials),
  
  // 注册
  register: (userData) => request.post('/api/auth/register', userData),
  
  // 第三方登录
  socialLogin: (provider, code) => request.post('/api/auth/social', { provider, code }),
  
  // 刷新 token
  refreshToken: () => request.post('/api/auth/refresh'),
  
  // 登出
  logout: () => request.post('/api/auth/logout')
}

// 用户相关 API
export const userAPI = {
  // 获取用户信息
  getUserInfo: () => request.get('/api/user/info'),
  
  // 更新用户资料
  updateProfile: (data) => request.put('/api/user/profile', data),
  
  // 上传头像
  uploadAvatar: (file) => {
    const formData = new FormData()
    formData.append('avatar', file)
    return request.post('/api/user/avatar', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    })
  },
  
  // 获取用户订阅信息
  getSubscription: () => request.get('/api/user/subscription'),
  
  // 获取用户消费记录
  getConsumption: () => request.get('/api/user/consumption')
}

// 股票分析相关 API
export const stockAPI = {
  // 搜索股票
  searchStock: (symbol) => request.get(`/api/stock/search?symbol=${symbol}`),
  
  // 获取股票分析
  getAnalysis: (symbol) => request.post('/api/stock/analysis', { symbol }),
  
  // 获取技术分析
  getTechnicalAnalysis: (symbol) => request.get(`/api/stock/technical/${symbol}`),
  
  // 获取大师分析
  getMasterAnalysis: (symbol) => request.get(`/api/stock/master/${symbol}`),
  
  // 获取股票历史数据
  getHistoryData: (symbol, period = '1y') => request.get(`/api/stock/history/${symbol}?period=${period}`)
}

// 新闻相关 API
export const newsAPI = {
  // 获取财经早报
  getFinancialNews: (page = 1, limit = 10) => request.get(`/api/news/financial?page=${page}&limit=${limit}`),
  
  // 获取股票相关新闻
  getStockNews: (symbol) => request.get(`/api/news/stock/${symbol}`),
  
  // 获取市场新闻
  getMarketNews: (page = 1, limit = 10) => request.get(`/api/news/market?page=${page}&limit=${limit}`)
}

// 市场数据相关 API
export const marketAPI = {
  // 获取全球主要指数
  getGlobalIndices: () => request.get('/api/market/indices'),
  
  // 获取市场概览
  getMarketOverview: () => request.get('/api/market/overview'),
  
  // 获取热门股票
  getHotStocks: () => request.get('/api/market/hot-stocks'),
  
  // 获取市场情绪指标
  getMarketSentiment: () => request.get('/api/market/sentiment')
}

// 订阅相关 API
export const subscriptionAPI = {
  // 获取订阅计划
  getPlans: () => request.get('/api/subscription/plans'),
  
  // 创建订阅
  subscribe: (planId, paymentMethod) => request.post('/api/subscription/subscribe', { planId, paymentMethod }),
  
  // 取消订阅
  cancel: () => request.post('/api/subscription/cancel'),
  
  // 更新订阅设置
  updateSettings: (settings) => request.put('/api/subscription/settings', settings),
  
  // 获取订阅历史
  getHistory: () => request.get('/api/subscription/history')
}

// 博客相关 API
export const blogAPI = {
  // 获取博客文章列表
  getPosts: (page = 1, limit = 10, category = '') => 
    request.get(`/api/blog/posts?page=${page}&limit=${limit}&category=${category}`),
  
  // 获取博客文章详情
  getPost: (id) => request.get(`/api/blog/posts/${id}`),
  
  // 获取博客分类
  getCategories: () => request.get('/api/blog/categories'),
  
  // 获取热门文章
  getPopularPosts: () => request.get('/api/blog/popular')
}

// 论坛相关 API
export const forumAPI = {
  // 获取论坛帖子列表
  getPosts: (page = 1, limit = 20, category = '') => 
    request.get(`/api/forum/posts?page=${page}&limit=${limit}&category=${category}`),
  
  // 获取帖子详情
  getPost: (id) => request.get(`/api/forum/posts/${id}`),
  
  // 创建帖子
  createPost: (data) => request.post('/api/forum/posts', data),
  
  // 回复帖子
  replyPost: (postId, content) => request.post(`/api/forum/posts/${postId}/replies`, { content }),
  
  // 点赞帖子
  likePost: (postId) => request.post(`/api/forum/posts/${postId}/like`),
  
  // 获取论坛分类
  getCategories: () => request.get('/api/forum/categories')
}

// 通用 API
export const commonAPI = {
  // 文件上传
  uploadFile: (file, type = 'image') => {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('type', type)
    return request.post('/api/upload', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    })
  },
  
  // 获取系统配置
  getConfig: () => request.get('/api/config'),
  
  // 发送反馈
  sendFeedback: (data) => request.post('/api/feedback', data)
}
