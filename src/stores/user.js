import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import axios from '@/utils/request'

export const useUserStore = defineStore('user', () => {
  // 状态
  const user = ref(null)
  const token = ref(localStorage.getItem('token') || '')
  const isLoading = ref(false)

  // 计算属性
  const isLoggedIn = computed(() => !!token.value && !!user.value)
  const isVip = computed(() => user.value?.subscription?.isActive || false)
  const userDisplayName = computed(() => user.value?.nickname || user.value?.username || '游客')
  const userAvatar = computed(() => user.value?.avatar || '/default-avatar.png')
  const remainingAnalyses = computed(() => {
    if (!user.value?.subscription) return 0
    return user.value.subscription.totalAnalyses - user.value.subscription.usedAnalyses
  })

  // 方法
  const login = async (credentials) => {
    try {
      isLoading.value = true
      const response = await axios.post('/api/auth/login', credentials)
      const { token: newToken, user: userData } = response.data
      
      token.value = newToken
      user.value = userData
      localStorage.setItem('token', newToken)
      
      return { success: true }
    } catch (error) {
      console.error('Login failed:', error)
      return { 
        success: false, 
        message: error.response?.data?.message || '登录失败' 
      }
    } finally {
      isLoading.value = false
    }
  }

  const register = async (userData) => {
    try {
      isLoading.value = true
      const response = await axios.post('/api/auth/register', userData)
      const { token: newToken, user: newUser } = response.data
      
      token.value = newToken
      user.value = newUser
      localStorage.setItem('token', newToken)
      
      return { success: true }
    } catch (error) {
      console.error('Registration failed:', error)
      return { 
        success: false, 
        message: error.response?.data?.message || '注册失败' 
      }
    } finally {
      isLoading.value = false
    }
  }

  const logout = () => {
    token.value = ''
    user.value = null
    localStorage.removeItem('token')
  }

  const updateProfile = async (profileData) => {
    try {
      isLoading.value = true
      const response = await axios.put('/api/user/profile', profileData)
      user.value = { ...user.value, ...response.data }
      return { success: true }
    } catch (error) {
      console.error('Profile update failed:', error)
      return { 
        success: false, 
        message: error.response?.data?.message || '更新失败' 
      }
    } finally {
      isLoading.value = false
    }
  }

  const uploadAvatar = async (file) => {
    try {
      isLoading.value = true
      const formData = new FormData()
      formData.append('avatar', file)
      
      const response = await axios.post('/api/user/avatar', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
      
      user.value.avatar = response.data.avatar
      return { success: true }
    } catch (error) {
      console.error('Avatar upload failed:', error)
      return { 
        success: false, 
        message: error.response?.data?.message || '头像上传失败' 
      }
    } finally {
      isLoading.value = false
    }
  }

  const fetchUserInfo = async () => {
    try {
      if (!token.value) return
      
      const response = await axios.get('/api/user/info')
      user.value = response.data
    } catch (error) {
      console.error('Failed to fetch user info:', error)
      // 如果获取用户信息失败，可能是token过期
      if (error.response?.status === 401) {
        logout()
      }
    }
  }

  const subscribe = async (planId) => {
    try {
      isLoading.value = true
      const response = await axios.post('/api/subscription/subscribe', { planId })
      user.value.subscription = response.data.subscription
      return { success: true }
    } catch (error) {
      console.error('Subscription failed:', error)
      return { 
        success: false, 
        message: error.response?.data?.message || '订阅失败' 
      }
    } finally {
      isLoading.value = false
    }
  }

  const cancelSubscription = async () => {
    try {
      isLoading.value = true
      await axios.post('/api/subscription/cancel')
      user.value.subscription.autoRenew = false
      return { success: true }
    } catch (error) {
      console.error('Cancel subscription failed:', error)
      return { 
        success: false, 
        message: error.response?.data?.message || '取消订阅失败' 
      }
    } finally {
      isLoading.value = false
    }
  }

  // 初始化时获取用户信息
  if (token.value) {
    fetchUserInfo()
  }

  return {
    // 状态
    user,
    token,
    isLoading,
    
    // 计算属性
    isLoggedIn,
    isVip,
    userDisplayName,
    userAvatar,
    remainingAnalyses,
    
    // 方法
    login,
    register,
    logout,
    updateProfile,
    uploadAvatar,
    fetchUserInfo,
    subscribe,
    cancelSubscription
  }
})
