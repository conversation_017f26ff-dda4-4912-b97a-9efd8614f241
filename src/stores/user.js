import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
// import axios from '@/utils/request' // 暂时注释掉，使用模拟数据

export const useUserStore = defineStore('user', () => {
  // 状态
  const user = ref(null)
  const token = ref(localStorage.getItem('token') || '')
  const isLoading = ref(false)

  // 计算属性
  const isLoggedIn = computed(() => !!token.value && !!user.value)
  const isVip = computed(() => user.value?.subscription?.isActive || false)
  const userDisplayName = computed(() => user.value?.nickname || user.value?.username || '游客')
  const userAvatar = computed(() => user.value?.avatar || '/default-avatar.png')
  const remainingAnalyses = computed(() => {
    if (!user.value?.subscription) return 0
    return user.value.subscription.totalAnalyses - user.value.subscription.usedAnalyses
  })

  // 方法 - 使用模拟数据
  const login = async (credentials) => {
    try {
      isLoading.value = true
      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 模拟登录成功
      const mockToken = 'mock-jwt-token-' + Date.now()
      const mockUser = {
        id: 1,
        username: credentials.username || 'demo_user',
        nickname: '演示用户',
        email: credentials.email || '<EMAIL>',
        avatar: '/default-avatar.png',
        subscription: {
          isActive: false,
          totalAnalyses: 10,
          usedAnalyses: 3
        }
      }

      token.value = mockToken
      user.value = mockUser
      localStorage.setItem('token', mockToken)

      return { success: true }
    } catch (error) {
      console.error('Login failed:', error)
      return {
        success: false,
        message: '登录失败'
      }
    } finally {
      isLoading.value = false
    }
  }

  const register = async (userData) => {
    try {
      isLoading.value = true
      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 模拟注册成功
      const mockToken = 'mock-jwt-token-' + Date.now()
      const mockUser = {
        id: Date.now(),
        username: userData.username,
        nickname: userData.nickname || userData.username,
        email: userData.email,
        avatar: '/default-avatar.png',
        subscription: {
          isActive: false,
          totalAnalyses: 5,
          usedAnalyses: 0
        }
      }

      token.value = mockToken
      user.value = mockUser
      localStorage.setItem('token', mockToken)

      return { success: true }
    } catch (error) {
      console.error('Registration failed:', error)
      return {
        success: false,
        message: '注册失败'
      }
    } finally {
      isLoading.value = false
    }
  }

  const logout = () => {
    token.value = ''
    user.value = null
    localStorage.removeItem('token')
  }

  const updateProfile = async (profileData) => {
    try {
      isLoading.value = true
      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 500))

      // 模拟更新成功
      user.value = { ...user.value, ...profileData }
      return { success: true }
    } catch (error) {
      console.error('Profile update failed:', error)
      return {
        success: false,
        message: '更新失败'
      }
    } finally {
      isLoading.value = false
    }
  }

  const uploadAvatar = async () => {
    try {
      isLoading.value = true
      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 模拟头像上传成功
      const mockAvatarUrl = '/mock-avatar-' + Date.now() + '.jpg'
      user.value.avatar = mockAvatarUrl
      return { success: true }
    } catch (error) {
      console.error('Avatar upload failed:', error)
      return {
        success: false,
        message: '头像上传失败'
      }
    } finally {
      isLoading.value = false
    }
  }

  const fetchUserInfo = async () => {
    try {
      if (!token.value) return

      // 模拟获取用户信息 - 如果有token但没有用户信息，创建模拟用户
      if (!user.value) {
        user.value = {
          id: 1,
          username: 'demo_user',
          nickname: '演示用户',
          email: '<EMAIL>',
          avatar: '/default-avatar.png',
          subscription: {
            isActive: false,
            totalAnalyses: 10,
            usedAnalyses: 3
          }
        }
      }
    } catch (error) {
      console.error('Failed to fetch user info:', error)
      // 如果获取用户信息失败，清除token
      logout()
    }
  }

  const subscribe = async (planId) => {
    try {
      isLoading.value = true
      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 模拟订阅成功
      user.value.subscription = {
        isActive: true,
        planId: planId,
        totalAnalyses: planId === 'basic' ? 100 : 1000,
        usedAnalyses: 0,
        autoRenew: true,
        expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30天后过期
      }
      return { success: true }
    } catch (error) {
      console.error('Subscription failed:', error)
      return {
        success: false,
        message: '订阅失败'
      }
    } finally {
      isLoading.value = false
    }
  }

  const cancelSubscription = async () => {
    try {
      isLoading.value = true
      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 500))

      // 模拟取消订阅成功
      if (user.value?.subscription) {
        user.value.subscription.autoRenew = false
      }
      return { success: true }
    } catch (error) {
      console.error('Cancel subscription failed:', error)
      return {
        success: false,
        message: '取消订阅失败'
      }
    } finally {
      isLoading.value = false
    }
  }

  // 初始化时获取用户信息（使用模拟数据）
  if (token.value) {
    fetchUserInfo()
  }

  return {
    // 状态
    user,
    token,
    isLoading,
    
    // 计算属性
    isLoggedIn,
    isVip,
    userDisplayName,
    userAvatar,
    remainingAnalyses,
    
    // 方法
    login,
    register,
    logout,
    updateProfile,
    uploadAvatar,
    fetchUserInfo,
    subscribe,
    cancelSubscription
  }
})
