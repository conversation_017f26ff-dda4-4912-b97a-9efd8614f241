<template>
  <div id="app">
    <div style="padding: 20px; text-align: center; background: white; min-height: 100vh;">
      <h1 style="color: #3b82f6; margin-bottom: 20px;">SmartCompass 财务分析平台</h1>
      <p style="margin-bottom: 20px;">正在逐步加载组件...</p>

      <div v-if="step >= 1" style="color: green; margin: 10px 0;">
        ✅ 步骤1: Vue应用基础功能正常
      </div>

      <div v-if="step >= 2" style="color: green; margin: 10px 0;">
        ✅ 步骤2: 路由系统正常
      </div>

      <div v-if="step >= 3" style="color: green; margin: 10px 0;">
        ✅ 步骤3: 状态管理正常
      </div>

      <div v-if="step >= 4" style="color: green; margin: 10px 0;">
        ✅ 步骤4: Element Plus组件正常
      </div>

      <div v-if="step >= 5">
        <SimpleHeader />
      </div>

      <div v-if="error" style="color: red; margin: 20px 0; padding: 10px; border: 1px solid red;">
        错误信息: {{ error }}
      </div>

      <el-button v-if="step >= 4" type="primary" @click="nextStep">
        继续下一步 ({{ step }}/5)
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onErrorCaptured } from 'vue'
import { useRouter } from 'vue-router'

const step = ref(0)
const error = ref('')
const router = useRouter()

// 简单的Header组件
const SimpleHeader = {
  template: `
    <div style="background: #f0f0f0; padding: 20px; margin: 20px 0; border-radius: 8px;">
      <h2 style="color: #3b82f6;">简化版导航栏</h2>
      <p>当前路由: {{ $route.path }}</p>
      <el-button @click="$router.push('/')">首页</el-button>
    </div>
  `
}

const nextStep = () => {
  if (step.value < 5) {
    step.value++
  }
}

onMounted(async () => {
  try {
    console.log('开始逐步测试...')

    // 步骤1: 基础Vue功能
    await new Promise(resolve => setTimeout(resolve, 500))
    step.value = 1

    // 步骤2: 路由测试
    console.log('测试路由:', router.currentRoute.value)
    await new Promise(resolve => setTimeout(resolve, 500))
    step.value = 2

    // 步骤3: Store测试 (简化版)
    console.log('测试状态管理...')
    await new Promise(resolve => setTimeout(resolve, 500))
    step.value = 3

    // 步骤4: Element Plus测试
    console.log('测试Element Plus...')
    await new Promise(resolve => setTimeout(resolve, 500))
    step.value = 4

    console.log('基础测试完成!')
  } catch (err) {
    console.error('测试过程中出错:', err)
    error.value = err.message
  }
})

onErrorCaptured((err, instance, info) => {
  console.error('Vue错误:', err, info)
  error.value = `Vue错误: ${err.message}`
  return false
})
</script>

<style>
#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  min-height: calc(100vh - 64px - 200px); /* 减去 header 和 footer 的大概高度 */
}

/* 路由过渡动画 */
.router-enter-active,
.router-leave-active {
  transition: all 0.3s ease;
}

.router-enter-from {
  opacity: 0;
  transform: translateX(20px);
}

.router-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}
</style>
