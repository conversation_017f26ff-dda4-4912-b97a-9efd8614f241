<template>
  <div id="app">
    <!-- 简化版导航栏 -->
    <header style="background: white; box-shadow: 0 2px 4px rgba(0,0,0,0.1); padding: 0 20px;">
      <div style="max-width: 1200px; margin: 0 auto; display: flex; align-items: center; justify-content: space-between; height: 64px;">
        <div style="display: flex; align-items: center; cursor: pointer;" @click="$router.push('/')">
          <el-icon size="32" color="#3b82f6" style="margin-right: 12px;">
            <Compass />
          </el-icon>
          <h1 style="font-size: 24px; font-weight: 700; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; margin: 0;">
            SmartCompass
          </h1>
        </div>

        <div style="display: flex; align-items: center; gap: 20px;">
          <el-button @click="$router.push('/')">首页</el-button>
          <el-button @click="showMessage">测试</el-button>
          <el-dropdown @command="changeLanguage">
            <span style="cursor: pointer; color: #666;">
              {{ currentLanguage }} <el-icon><ArrowDown /></el-icon>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="zh">中文</el-dropdown-item>
                <el-dropdown-item command="en">English</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <main style="min-height: calc(100vh - 64px);">
      <router-view />
    </main>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { ElMessage } from 'element-plus'
import { Compass, ArrowDown } from '@element-plus/icons-vue'

const { locale } = useI18n()

const currentLanguage = computed(() => locale.value === 'zh' ? '中文' : 'EN')

const showMessage = () => {
  ElMessage.success('SmartCompass 正常运行！')
}

const changeLanguage = (lang) => {
  locale.value = lang
  localStorage.setItem('locale', lang)
  ElMessage.success(`已切换到${lang === 'zh' ? '中文' : 'English'}`)
}
</script>

<style>
#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  min-height: calc(100vh - 64px - 200px); /* 减去 header 和 footer 的大概高度 */
}

/* 路由过渡动画 */
.router-enter-active,
.router-leave-active {
  transition: all 0.3s ease;
}

.router-enter-from {
  opacity: 0;
  transform: translateX(20px);
}

.router-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}
</style>
