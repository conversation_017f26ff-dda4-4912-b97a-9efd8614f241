<template>
  <div id="app">
    <Header />
    <main class="main-content">
      <router-view />
    </main>
    <Footer />
  </div>
</template>

<script setup>
import { onMounted } from 'vue'
import Header from '@/components/Layout/Header.vue'
import Footer from '@/components/Layout/Footer.vue'

onMounted(() => {
  console.log('SmartCompass App mounted successfully!')
})
</script>

<style>
#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  min-height: calc(100vh - 64px - 200px); /* 减去 header 和 footer 的大概高度 */
}

/* 路由过渡动画 */
.router-enter-active,
.router-leave-active {
  transition: all 0.3s ease;
}

.router-enter-from {
  opacity: 0;
  transform: translateX(20px);
}

.router-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}
</style>
