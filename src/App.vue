<template>
  <div id="app">
    <div style="padding: 20px; text-align: center; background: white; min-height: 100vh;">
      <h1 style="color: #3b82f6; margin-bottom: 20px;">SmartCompass 财务分析平台</h1>
      <p style="margin-bottom: 20px;">应用正在加载中...</p>
      <div v-if="error" style="color: red; margin-bottom: 20px;">
        错误信息: {{ error }}
      </div>
      <div v-if="mounted" style="color: green;">
        ✅ Vue应用已成功挂载
      </div>
      <div v-if="routerReady" style="color: green;">
        ✅ 路由已准备就绪
      </div>
      <div v-if="storeReady" style="color: green;">
        ✅ 状态管理已准备就绪
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onErrorCaptured } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'

const mounted = ref(false)
const routerReady = ref(false)
const storeReady = ref(false)
const error = ref('')

const router = useRouter()

onMounted(async () => {
  try {
    console.log('App mounting...')
    mounted.value = true

    // 测试路由
    console.log('Testing router...')
    console.log('Current route:', router.currentRoute.value)
    routerReady.value = true

    // 测试store
    console.log('Testing store...')
    const userStore = useUserStore()
    console.log('User store:', userStore)
    storeReady.value = true

    console.log('SmartCompass App mounted successfully!')
  } catch (err) {
    console.error('App mount error:', err)
    error.value = err.message
  }
})

onErrorCaptured((err, instance, info) => {
  console.error('Vue error captured:', err, info)
  error.value = `Vue错误: ${err.message}`
  return false
})
</script>

<style>
#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  min-height: calc(100vh - 64px - 200px); /* 减去 header 和 footer 的大概高度 */
}

/* 路由过渡动画 */
.router-enter-active,
.router-leave-active {
  transition: all 0.3s ease;
}

.router-enter-from {
  opacity: 0;
  transform: translateX(20px);
}

.router-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}
</style>
