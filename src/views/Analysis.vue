<template>
  <div class="analysis-page">
    <div class="container">
      <!-- 页面头部 -->
      <div class="page-header">
        <div class="stock-info">
          <h1 class="stock-symbol">{{ symbol }}</h1>
          <div class="stock-meta">
            <span class="stock-name">{{ stockData.name || '加载中...' }}</span>
            <div class="stock-price" :class="priceChangeClass">
              <span class="current-price">${{ stockData.price || '---' }}</span>
              <span class="price-change">{{ stockData.change || '---' }}</span>
              <span class="price-change-percent">{{ stockData.changePercent || '---' }}</span>
            </div>
          </div>
        </div>
        <div class="action-buttons">
          <el-button @click="$router.go(-1)">
            <el-icon><ArrowLeft /></el-icon>
            返回
          </el-button>
          <el-button type="primary" @click="refreshAnalysis" :loading="isLoading">
            <el-icon><Refresh /></el-icon>
            刷新分析
          </el-button>
        </div>
      </div>

      <!-- 分析结果概览 -->
      <div class="analysis-overview">
        <div class="overview-cards">
          <div class="overview-card recommendation-card">
            <div class="card-header">
              <h3>AI 推荐</h3>
              <div class="recommendation-badge" :class="recommendation.type">
                {{ recommendation.text }}
              </div>
            </div>
            <div class="card-body">
              <div class="recommendation-details">
                <div class="detail-item">
                  <span class="label">建议买入点</span>
                  <span class="value">${{ recommendation.buyPoint }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">建议卖出点</span>
                  <span class="value">${{ recommendation.sellPoint }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">置信度</span>
                  <span class="value">{{ recommendation.confidence }}%</span>
                </div>
              </div>
            </div>
          </div>

          <div class="overview-card risk-card">
            <div class="card-header">
              <h3>风险评估</h3>
              <div class="risk-level" :class="riskAssessment.level">
                {{ riskAssessment.text }}
              </div>
            </div>
            <div class="card-body">
              <el-progress
                :percentage="riskAssessment.score"
                :color="riskAssessment.color"
                :stroke-width="12"
              />
              <p class="risk-description">{{ riskAssessment.description }}</p>
            </div>
          </div>

          <div class="overview-card target-card">
            <div class="card-header">
              <h3>价格目标</h3>
            </div>
            <div class="card-body">
              <div class="target-prices">
                <div class="target-item">
                  <span class="target-label">保守目标</span>
                  <span class="target-value conservative">${{ priceTargets.conservative }}</span>
                </div>
                <div class="target-item">
                  <span class="target-label">中性目标</span>
                  <span class="target-value neutral">${{ priceTargets.neutral }}</span>
                </div>
                <div class="target-item">
                  <span class="target-label">乐观目标</span>
                  <span class="target-value optimistic">${{ priceTargets.optimistic }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 详细分析内容 -->
      <div class="analysis-content">
        <div class="content-grid">
          <!-- 技术分析 -->
          <div class="analysis-section technical-analysis">
            <div class="section-header">
              <h2 class="section-title">
                <el-icon><TrendCharts /></el-icon>
                技术分析
              </h2>
            </div>
            <div class="section-body">
              <div v-if="technicalLoading" class="loading-container">
                <el-skeleton :rows="4" animated />
              </div>
              <div v-else class="technical-content">
                <!-- K线图 -->
                <div class="chart-container">
                  <h4>价格走势图</h4>
                  <div class="chart-placeholder">
                    <el-empty description="图表加载中..." />
                  </div>
                </div>
                
                <!-- 技术指标 -->
                <div class="indicators-grid">
                  <div
                    v-for="indicator in technicalIndicators"
                    :key="indicator.name"
                    class="indicator-card"
                  >
                    <div class="indicator-header">
                      <span class="indicator-name">{{ indicator.name }}</span>
                      <span class="indicator-signal" :class="indicator.signal">
                        {{ indicator.signalText }}
                      </span>
                    </div>
                    <div class="indicator-value">{{ indicator.value }}</div>
                    <div class="indicator-description">{{ indicator.description }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 大师分析 -->
          <div class="analysis-section master-analysis">
            <div class="section-header">
              <h2 class="section-title">
                <el-icon><Star /></el-icon>
                大师观点
              </h2>
            </div>
            <div class="section-body">
              <div v-if="masterLoading" class="loading-container">
                <el-skeleton :rows="3" animated />
              </div>
              <div v-else class="master-content">
                <div
                  v-for="master in masterAnalyses"
                  :key="master.id"
                  class="master-card"
                >
                  <div class="master-header">
                    <div class="master-info">
                      <el-avatar :src="master.avatar" :size="48" />
                      <div class="master-details">
                        <h4 class="master-name">{{ master.name }}</h4>
                        <p class="master-title">{{ master.title }}</p>
                      </div>
                    </div>
                    <div class="master-rating">
                      <el-rate v-model="master.rating" disabled show-score />
                    </div>
                  </div>
                  <div class="master-analysis">
                    <p>{{ master.analysis }}</p>
                  </div>
                  <div class="master-recommendation">
                    <el-tag :type="master.recommendationType">
                      {{ master.recommendation }}
                    </el-tag>
                    <span class="target-price">目标价: ${{ master.targetPrice }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute } from 'vue-router'
import { stockAPI } from '@/api'
import {
  ArrowLeft,
  Refresh,
  TrendCharts,
  Star
} from '@element-plus/icons-vue'

const route = useRoute()
const symbol = ref(route.params.symbol || '')

// 响应式数据
const isLoading = ref(false)
const technicalLoading = ref(false)
const masterLoading = ref(false)
const stockData = ref({})
const recommendation = ref({})
const riskAssessment = ref({})
const priceTargets = ref({})
const technicalIndicators = ref([])
const masterAnalyses = ref([])

// 计算属性
const priceChangeClass = computed(() => {
  const change = parseFloat(stockData.value.change)
  if (change > 0) return 'positive'
  if (change < 0) return 'negative'
  return 'neutral'
})

// 方法
const fetchStockData = async () => {
  try {
    const response = await stockAPI.searchStock(symbol.value)
    stockData.value = response.data || mockStockData
  } catch (error) {
    console.error('Failed to fetch stock data:', error)
    stockData.value = mockStockData
  }
}

const fetchAnalysis = async () => {
  try {
    isLoading.value = true
    const response = await stockAPI.getAnalysis(symbol.value)
    const data = response.data || mockAnalysisData
    
    recommendation.value = data.recommendation
    riskAssessment.value = data.riskAssessment
    priceTargets.value = data.priceTargets
  } catch (error) {
    console.error('Failed to fetch analysis:', error)
    // 使用模拟数据
    recommendation.value = mockAnalysisData.recommendation
    riskAssessment.value = mockAnalysisData.riskAssessment
    priceTargets.value = mockAnalysisData.priceTargets
  } finally {
    isLoading.value = false
  }
}

const fetchTechnicalAnalysis = async () => {
  try {
    technicalLoading.value = true
    const response = await stockAPI.getTechnicalAnalysis(symbol.value)
    technicalIndicators.value = response.data || mockTechnicalData
  } catch (error) {
    console.error('Failed to fetch technical analysis:', error)
    technicalIndicators.value = mockTechnicalData
  } finally {
    technicalLoading.value = false
  }
}

const fetchMasterAnalysis = async () => {
  try {
    masterLoading.value = true
    const response = await stockAPI.getMasterAnalysis(symbol.value)
    masterAnalyses.value = response.data || mockMasterData
  } catch (error) {
    console.error('Failed to fetch master analysis:', error)
    masterAnalyses.value = mockMasterData
  } finally {
    masterLoading.value = false
  }
}

const refreshAnalysis = () => {
  fetchAnalysis()
  fetchTechnicalAnalysis()
  fetchMasterAnalysis()
}

// 模拟数据
const mockStockData = {
  name: 'Apple Inc.',
  price: '175.43',
  change: '+2.15',
  changePercent: '+1.24%'
}

const mockAnalysisData = {
  recommendation: {
    type: 'buy',
    text: '买入',
    buyPoint: '170.00',
    sellPoint: '185.00',
    confidence: 78
  },
  riskAssessment: {
    level: 'medium',
    text: '中等风险',
    score: 65,
    color: '#f59e0b',
    description: '该股票具有中等风险水平，建议适度配置'
  },
  priceTargets: {
    conservative: '180.00',
    neutral: '190.00',
    optimistic: '205.00'
  }
}

const mockTechnicalData = [
  {
    name: 'RSI',
    value: '68.5',
    signal: 'neutral',
    signalText: '中性',
    description: 'RSI指标显示股票处于中性区域'
  },
  {
    name: 'MACD',
    value: '1.25',
    signal: 'bullish',
    signalText: '看涨',
    description: 'MACD金叉，显示上涨趋势'
  }
]

const mockMasterData = [
  {
    id: 1,
    name: '巴菲特策略',
    title: '价值投资大师',
    avatar: '/avatars/buffett.jpg',
    rating: 4.8,
    analysis: '苹果公司具有强大的品牌护城河和稳定的现金流，是优质的长期投资标的。',
    recommendation: '买入',
    recommendationType: 'success',
    targetPrice: '190.00'
  }
]

// 监听路由参数变化
watch(() => route.params.symbol, (newSymbol) => {
  if (newSymbol) {
    symbol.value = newSymbol
    fetchStockData()
    refreshAnalysis()
  }
})

onMounted(() => {
  if (symbol.value) {
    fetchStockData()
    refreshAnalysis()
  }
})
</script>

<style scoped>
.analysis-page {
  padding: 40px 0;
  min-height: 100vh;
  background: var(--bg-secondary);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 40px;
  padding: 24px;
  background: white;
  border-radius: 16px;
  box-shadow: var(--shadow-md);
}

.stock-info {
  flex: 1;
}

.stock-symbol {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 8px;
}

.stock-meta {
  display: flex;
  align-items: center;
  gap: 24px;
}

.stock-name {
  font-size: 1.1rem;
  color: var(--text-secondary);
}

.stock-price {
  display: flex;
  align-items: center;
  gap: 8px;
}

.current-price {
  font-size: 1.5rem;
  font-weight: 600;
}

.price-change,
.price-change-percent {
  font-weight: 500;
}

.stock-price.positive .price-change,
.stock-price.positive .price-change-percent {
  color: var(--success-color);
}

.stock-price.negative .price-change,
.stock-price.negative .price-change-percent {
  color: var(--error-color);
}

.action-buttons {
  display: flex;
  gap: 12px;
}

.analysis-overview {
  margin-bottom: 40px;
}

.overview-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

.overview-card {
  background: white;
  border-radius: 16px;
  box-shadow: var(--shadow-md);
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid var(--border-color);
}

.card-header h3 {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
}

.recommendation-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
}

.recommendation-badge.buy {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.recommendation-badge.sell {
  background: rgba(239, 68, 68, 0.1);
  color: var(--error-color);
}

.recommendation-badge.hold {
  background: rgba(107, 114, 128, 0.1);
  color: var(--text-secondary);
}

.risk-level {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
}

.risk-level.low {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.risk-level.medium {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
}

.risk-level.high {
  background: rgba(239, 68, 68, 0.1);
  color: var(--error-color);
}

.card-body {
  padding: 24px;
}

.recommendation-details {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.label {
  color: var(--text-secondary);
  font-size: 14px;
}

.value {
  font-weight: 600;
  color: var(--text-primary);
}

.risk-description {
  margin-top: 16px;
  color: var(--text-secondary);
  font-size: 14px;
  line-height: 1.5;
}

.target-prices {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.target-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.target-value {
  font-weight: 600;
  font-size: 16px;
}

.target-value.conservative {
  color: var(--success-color);
}

.target-value.neutral {
  color: var(--warning-color);
}

.target-value.optimistic {
  color: var(--primary-color);
}

.analysis-content {
  margin-bottom: 40px;
}

.content-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
}

.analysis-section {
  background: white;
  border-radius: 16px;
  box-shadow: var(--shadow-md);
  overflow: hidden;
}

.section-header {
  padding: 24px;
  border-bottom: 1px solid var(--border-color);
  background: var(--bg-secondary);
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
}

.section-body {
  padding: 24px;
}

.loading-container {
  padding: 20px 0;
}

.chart-container {
  margin-bottom: 32px;
}

.chart-container h4 {
  margin-bottom: 16px;
  color: var(--text-primary);
}

.chart-placeholder {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-secondary);
  border-radius: 8px;
}

.indicators-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.indicator-card {
  padding: 16px;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.indicator-card:hover {
  box-shadow: var(--shadow-sm);
}

.indicator-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.indicator-name {
  font-weight: 600;
  color: var(--text-primary);
}

.indicator-signal {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.indicator-signal.bullish {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.indicator-signal.bearish {
  background: rgba(239, 68, 68, 0.1);
  color: var(--error-color);
}

.indicator-signal.neutral {
  background: rgba(107, 114, 128, 0.1);
  color: var(--text-secondary);
}

.indicator-value {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.indicator-description {
  font-size: 12px;
  color: var(--text-muted);
  line-height: 1.4;
}

.master-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.master-card {
  padding: 20px;
  border: 1px solid var(--border-color);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.master-card:hover {
  box-shadow: var(--shadow-sm);
}

.master-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.master-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.master-name {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 4px 0;
}

.master-title {
  font-size: 14px;
  color: var(--text-secondary);
  margin: 0;
}

.master-analysis {
  margin-bottom: 16px;
  line-height: 1.6;
  color: var(--text-primary);
}

.master-recommendation {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.target-price {
  font-weight: 500;
  color: var(--text-secondary);
}

@media (max-width: 1024px) {
  .content-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 20px;
    align-items: stretch;
  }

  .stock-meta {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .overview-cards {
    grid-template-columns: 1fr;
  }

  .indicators-grid {
    grid-template-columns: 1fr;
  }
}
</style>
