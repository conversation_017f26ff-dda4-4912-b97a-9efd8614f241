<template>
  <div class="my-page">
    <div class="container">
      <div class="page-header">
        <h1 class="page-title">我的账户</h1>
        <p class="page-description">管理您的个人信息和订阅设置</p>
      </div>

      <div class="content-grid">
        <!-- 用户信息卡片 -->
        <div class="info-card user-info-card">
          <div class="card-header">
            <h2 class="card-title">
              <el-icon><User /></el-icon>
              个人信息
            </h2>
            <el-button type="primary" text @click="showEditProfile = true">
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>
          </div>
          <div class="card-body">
            <div class="user-profile">
              <div class="avatar-section">
                <el-upload
                  class="avatar-uploader"
                  :show-file-list="false"
                  :before-upload="beforeAvatarUpload"
                  :http-request="handleAvatarUpload"
                >
                  <el-avatar :size="80" :src="userStore.userAvatar" />
                  <div class="avatar-overlay">
                    <el-icon><Camera /></el-icon>
                  </div>
                </el-upload>
                <div v-if="userStore.isVip" class="vip-badge">VIP</div>
              </div>
              <div class="user-details">
                <h3 class="username">{{ userStore.userDisplayName }}</h3>
                <p class="user-email">{{ userStore.user?.email || '未设置邮箱' }}</p>
                <p class="user-phone">{{ userStore.user?.phone || '未设置手机号' }}</p>
                <div class="user-stats">
                  <div class="stat-item">
                    <span class="stat-label">注册时间</span>
                    <span class="stat-value">{{ formatDate(userStore.user?.createdAt) }}</span>
                  </div>
                  <div class="stat-item">
                    <span class="stat-label">账户余额</span>
                    <span class="stat-value">¥{{ userStore.user?.balance || 0 }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 订阅信息卡片 -->
        <div class="info-card subscription-card">
          <div class="card-header">
            <h2 class="card-title">
              <el-icon><Star /></el-icon>
              订阅信息
            </h2>
            <el-button 
              v-if="!userStore.isVip" 
              type="primary" 
              @click="$router.push('/pricing')"
            >
              立即订阅
            </el-button>
          </div>
          <div class="card-body">
            <div v-if="userStore.isVip" class="subscription-active">
              <div class="subscription-status">
                <el-tag type="success" size="large">
                  <el-icon><Check /></el-icon>
                  VIP 会员
                </el-tag>
              </div>
              <div class="subscription-details">
                <div class="detail-item">
                  <span class="detail-label">订阅计划</span>
                  <span class="detail-value">{{ userStore.user?.subscription?.planName || '专业版' }}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">到期时间</span>
                  <span class="detail-value">{{ formatDate(userStore.user?.subscription?.expiresAt) }}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">自动续费</span>
                  <el-switch
                    v-model="autoRenew"
                    @change="handleAutoRenewChange"
                  />
                </div>
              </div>
            </div>
            <div v-else class="subscription-inactive">
              <el-empty description="您还未订阅任何计划">
                <el-button type="primary" @click="$router.push('/pricing')">
                  查看订阅计划
                </el-button>
              </el-empty>
            </div>
          </div>
        </div>

        <!-- 使用统计卡片 -->
        <div class="info-card usage-card">
          <div class="card-header">
            <h2 class="card-title">
              <el-icon><DataAnalysis /></el-icon>
              使用统计
            </h2>
          </div>
          <div class="card-body">
            <div class="usage-stats">
              <div class="usage-item">
                <div class="usage-header">
                  <span class="usage-label">分析次数</span>
                  <span class="usage-count">
                    {{ usedAnalyses }} / {{ totalAnalyses }}
                  </span>
                </div>
                <el-progress
                  :percentage="analysisPercentage"
                  :color="progressColor"
                  :stroke-width="8"
                />
              </div>
              <div class="usage-details">
                <div class="detail-row">
                  <span>本月已使用</span>
                  <span>{{ usedAnalyses }} 次</span>
                </div>
                <div class="detail-row">
                  <span>剩余次数</span>
                  <span>{{ remainingAnalyses }} 次</span>
                </div>
                <div class="detail-row">
                  <span>重置时间</span>
                  <span>{{ nextResetDate }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 最近活动 -->
        <div class="info-card activity-card">
          <div class="card-header">
            <h2 class="card-title">
              <el-icon><Clock /></el-icon>
              最近活动
            </h2>
          </div>
          <div class="card-body">
            <div class="activity-list">
              <div
                v-for="activity in recentActivities"
                :key="activity.id"
                class="activity-item"
              >
                <div class="activity-icon">
                  <el-icon :color="activity.iconColor">
                    <component :is="activity.icon" />
                  </el-icon>
                </div>
                <div class="activity-content">
                  <p class="activity-title">{{ activity.title }}</p>
                  <p class="activity-time">{{ formatTime(activity.time) }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 编辑资料弹窗 -->
    <el-dialog
      v-model="showEditProfile"
      title="编辑个人资料"
      width="500px"
    >
      <el-form :model="profileForm" label-width="80px">
        <el-form-item label="昵称">
          <el-input v-model="profileForm.nickname" />
        </el-form-item>
        <el-form-item label="邮箱">
          <el-input v-model="profileForm.email" />
        </el-form-item>
        <el-form-item label="手机号">
          <el-input v-model="profileForm.phone" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showEditProfile = false">取消</el-button>
        <el-button type="primary" @click="handleUpdateProfile">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'
import {
  User,
  Edit,
  Camera,
  Star,
  Check,
  DataAnalysis,
  Clock,
  TrendCharts,
  CreditCard,
  Setting
} from '@element-plus/icons-vue'

const userStore = useUserStore()

// 响应式数据
const showEditProfile = ref(false)
const autoRenew = ref(true)
const profileForm = ref({
  nickname: '',
  email: '',
  phone: ''
})

// 计算属性
const usedAnalyses = computed(() => userStore.user?.subscription?.usedAnalyses || 0)
const totalAnalyses = computed(() => userStore.user?.subscription?.totalAnalyses || 100)
const remainingAnalyses = computed(() => totalAnalyses.value - usedAnalyses.value)
const analysisPercentage = computed(() => 
  totalAnalyses.value > 0 ? Math.round((usedAnalyses.value / totalAnalyses.value) * 100) : 0
)

const progressColor = computed(() => {
  const percentage = analysisPercentage.value
  if (percentage < 50) return '#10b981'
  if (percentage < 80) return '#f59e0b'
  return '#ef4444'
})

const nextResetDate = computed(() => {
  const now = new Date()
  const nextMonth = new Date(now.getFullYear(), now.getMonth() + 1, 1)
  return formatDate(nextMonth)
})

// 模拟最近活动数据
const recentActivities = ref([
  {
    id: 1,
    title: '分析了 AAPL 股票',
    time: new Date(Date.now() - 3600000),
    icon: 'TrendCharts',
    iconColor: '#3b82f6'
  },
  {
    id: 2,
    title: '订阅了专业版',
    time: new Date(Date.now() - 86400000),
    icon: 'Star',
    iconColor: '#10b981'
  },
  {
    id: 3,
    title: '更新了个人资料',
    time: new Date(Date.now() - 172800000),
    icon: 'Setting',
    iconColor: '#8b5cf6'
  }
])

// 方法
const formatDate = (date) => {
  if (!date) return '未知'
  return new Date(date).toLocaleDateString('zh-CN')
}

const formatTime = (time) => {
  return new Date(time).toLocaleString('zh-CN', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const beforeAvatarUpload = (file) => {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return false
  }
  return true
}

const handleAvatarUpload = async (options) => {
  const result = await userStore.uploadAvatar(options.file)
  if (result.success) {
    ElMessage.success('头像上传成功')
  } else {
    ElMessage.error(result.message)
  }
}

const handleUpdateProfile = async () => {
  const result = await userStore.updateProfile(profileForm.value)
  if (result.success) {
    ElMessage.success('资料更新成功')
    showEditProfile.value = false
  } else {
    ElMessage.error(result.message)
  }
}

const handleAutoRenewChange = async (value) => {
  // TODO: 调用 API 更新自动续费设置
  ElMessage.success(value ? '已开启自动续费' : '已关闭自动续费')
}

onMounted(() => {
  // 初始化表单数据
  if (userStore.user) {
    profileForm.value = {
      nickname: userStore.user.nickname || '',
      email: userStore.user.email || '',
      phone: userStore.user.phone || ''
    }
    autoRenew.value = userStore.user.subscription?.autoRenew || false
  }
})
</script>

<style scoped>
.my-page {
  padding: 40px 0;
  min-height: 100vh;
  background: var(--bg-secondary);
}

.page-header {
  text-align: center;
  margin-bottom: 40px;
}

.page-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 12px;
}

.page-description {
  color: var(--text-secondary);
  font-size: 1.1rem;
}

.content-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 30px;
  max-width: 1200px;
  margin: 0 auto;
}

.info-card {
  background: white;
  border-radius: 16px;
  box-shadow: var(--shadow-md);
  overflow: hidden;
  transition: all 0.3s ease;
}

.info-card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  border-bottom: 1px solid var(--border-color);
  background: var(--bg-secondary);
}

.card-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
}

.card-body {
  padding: 24px;
}

.user-profile {
  display: flex;
  gap: 24px;
  align-items: flex-start;
}

.avatar-section {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.avatar-uploader {
  position: relative;
  cursor: pointer;
}

.avatar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 50%;
  color: white;
}

.avatar-uploader:hover .avatar-overlay {
  opacity: 1;
}

.user-details {
  flex: 1;
}

.username {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 8px;
}

.user-email,
.user-phone {
  color: var(--text-secondary);
  margin-bottom: 4px;
}

.user-stats {
  display: flex;
  gap: 24px;
  margin-top: 16px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat-label {
  font-size: 12px;
  color: var(--text-muted);
  text-transform: uppercase;
}

.stat-value {
  font-weight: 600;
  color: var(--text-primary);
}

.subscription-active {
  text-align: center;
}

.subscription-status {
  margin-bottom: 24px;
}

.subscription-details {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.detail-label {
  color: var(--text-secondary);
}

.detail-value {
  font-weight: 500;
}

.usage-stats {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.usage-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.usage-label {
  font-weight: 500;
}

.usage-count {
  font-weight: 600;
  color: var(--primary-color);
}

.usage-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
}

.detail-row:first-child {
  color: var(--text-secondary);
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border-radius: 8px;
  transition: background 0.3s ease;
}

.activity-item:hover {
  background: var(--bg-secondary);
}

.activity-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-secondary);
  border-radius: 8px;
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-weight: 500;
  margin-bottom: 4px;
}

.activity-time {
  font-size: 12px;
  color: var(--text-muted);
  margin: 0;
}

@media (max-width: 768px) {
  .content-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .user-profile {
    flex-direction: column;
    text-align: center;
  }
  
  .user-stats {
    justify-content: center;
  }
}
</style>
