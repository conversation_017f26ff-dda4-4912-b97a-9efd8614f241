<template>
  <div class="pricing-page">
    <div class="container">
      <!-- 页面头部 -->
      <div class="page-header">
        <h1 class="page-title">选择适合您的计划</h1>
        <p class="page-description">
          从免费试用到专业版，找到最适合您投资需求的订阅计划
        </p>
      </div>

      <!-- 计费周期切换 -->
      <div class="billing-toggle">
        <el-radio-group v-model="billingCycle" class="billing-options">
          <el-radio-button label="monthly">月付</el-radio-button>
          <el-radio-button label="yearly">年付</el-radio-button>
        </el-radio-group>
        <div class="discount-badge" v-if="billingCycle === 'yearly'">
          <el-tag type="success">年付享8折优惠</el-tag>
        </div>
      </div>

      <!-- 定价卡片 -->
      <div class="pricing-cards">
        <div
          v-for="plan in pricingPlans"
          :key="plan.id"
          :class="['pricing-card', { popular: plan.popular, current: plan.current }]"
        >
          <div v-if="plan.popular" class="popular-badge">
            <el-icon><Star /></el-icon>
            最受欢迎
          </div>
          
          <div class="plan-header">
            <h3 class="plan-name">{{ plan.name }}</h3>
            <div class="plan-price">
              <span class="currency">¥</span>
              <span class="amount">{{ getCurrentPrice(plan) }}</span>
              <span class="period">{{ billingCycle === 'monthly' ? '/月' : '/年' }}</span>
            </div>
            <p class="plan-description">{{ plan.description }}</p>
          </div>

          <div class="plan-features">
            <div
              v-for="feature in plan.features"
              :key="feature.name"
              class="feature-item"
            >
              <el-icon class="feature-icon" :color="feature.included ? '#10b981' : '#d1d5db'">
                <Check v-if="feature.included" />
                <Close v-else />
              </el-icon>
              <span :class="{ disabled: !feature.included }">{{ feature.name }}</span>
            </div>
          </div>

          <div class="plan-action">
            <el-button
              v-if="plan.current"
              type="info"
              size="large"
              class="action-button"
              disabled
            >
              当前计划
            </el-button>
            <el-button
              v-else-if="plan.id === 'free'"
              type="default"
              size="large"
              class="action-button"
              @click="handleFreeTrial"
            >
              免费试用
            </el-button>
            <el-button
              v-else
              type="primary"
              size="large"
              class="action-button"
              @click="handleSubscribe(plan)"
              :loading="subscribing === plan.id"
            >
              立即订阅
            </el-button>
          </div>
        </div>
      </div>

      <!-- 常见问题 -->
      <div class="faq-section">
        <h2 class="section-title">常见问题</h2>
        <el-collapse v-model="activeCollapse" class="faq-collapse">
          <el-collapse-item
            v-for="faq in faqs"
            :key="faq.id"
            :title="faq.question"
            :name="faq.id"
          >
            <p>{{ faq.answer }}</p>
          </el-collapse-item>
        </el-collapse>
      </div>

      <!-- 联系支持 -->
      <div class="support-section">
        <div class="support-card">
          <h3>需要帮助？</h3>
          <p>我们的专业团队随时为您提供支持</p>
          <el-button type="primary" @click="contactSupport">
            联系客服
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'
import { Star, Check, Close } from '@element-plus/icons-vue'

const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const billingCycle = ref('monthly')
const subscribing = ref('')
const activeCollapse = ref(['1'])

// 定价计划
const pricingPlans = ref([
  {
    id: 'free',
    name: '免费版',
    description: '适合初学者体验基础功能',
    monthlyPrice: 0,
    yearlyPrice: 0,
    popular: false,
    current: false,
    features: [
      { name: '每月5次股票分析', included: true },
      { name: '基础技术指标', included: true },
      { name: '财经新闻推送', included: true },
      { name: '社区论坛访问', included: true },
      { name: 'AI智能推荐', included: false },
      { name: '实时数据推送', included: false },
      { name: '专业技术分析', included: false },
      { name: '大师观点解读', included: false },
      { name: '优先客服支持', included: false }
    ]
  },
  {
    id: 'pro',
    name: '专业版',
    description: '适合活跃投资者的全功能体验',
    monthlyPrice: 99,
    yearlyPrice: 950,
    popular: true,
    current: false,
    features: [
      { name: '每月100次股票分析', included: true },
      { name: '全套技术指标', included: true },
      { name: '实时财经新闻', included: true },
      { name: '社区论坛访问', included: true },
      { name: 'AI智能推荐', included: true },
      { name: '实时数据推送', included: true },
      { name: '专业技术分析', included: true },
      { name: '大师观点解读', included: true },
      { name: '优先客服支持', included: false }
    ]
  },
  {
    id: 'premium',
    name: '旗舰版',
    description: '专业投资者的终极选择',
    monthlyPrice: 199,
    yearlyPrice: 1900,
    popular: false,
    current: false,
    features: [
      { name: '无限次股票分析', included: true },
      { name: '全套技术指标', included: true },
      { name: '实时财经新闻', included: true },
      { name: '社区论坛访问', included: true },
      { name: 'AI智能推荐', included: true },
      { name: '实时数据推送', included: true },
      { name: '专业技术分析', included: true },
      { name: '大师观点解读', included: true },
      { name: '优先客服支持', included: true }
    ]
  }
])

// 常见问题
const faqs = ref([
  {
    id: '1',
    question: '如何取消订阅？',
    answer: '您可以在账户设置中随时取消订阅。取消后，您仍可使用服务直到当前计费周期结束。'
  },
  {
    id: '2',
    question: '是否支持退款？',
    answer: '我们提供7天无理由退款保证。如果您在订阅后7天内不满意，可以申请全额退款。'
  },
  {
    id: '3',
    question: '可以随时升级或降级计划吗？',
    answer: '是的，您可以随时升级或降级您的订阅计划。费用将按比例调整。'
  },
  {
    id: '4',
    question: '分析次数如何计算？',
    answer: '每次点击"分析"按钮并获得完整分析报告时，会消耗一次分析次数。查看历史分析不会消耗次数。'
  }
])

// 计算属性
const getCurrentPrice = computed(() => (plan) => {
  return billingCycle.value === 'monthly' ? plan.monthlyPrice : plan.yearlyPrice
})

// 方法
const handleFreeTrial = () => {
  if (!userStore.isLoggedIn) {
    router.push('/auth')
    return
  }
  ElMessage.success('免费试用已激活！')
}

const handleSubscribe = async (plan) => {
  if (!userStore.isLoggedIn) {
    router.push('/auth')
    return
  }

  subscribing.value = plan.id
  
  try {
    const result = await userStore.subscribe(plan.id)
    if (result.success) {
      ElMessage.success('订阅成功！')
      // 更新当前计划状态
      pricingPlans.value.forEach(p => {
        p.current = p.id === plan.id
      })
    } else {
      ElMessage.error(result.message)
    }
  } catch (error) {
    ElMessage.error('订阅失败，请稍后重试')
  } finally {
    subscribing.value = ''
  }
}

const contactSupport = () => {
  ElMessage.info('客服功能开发中...')
}
</script>

<style scoped>
.pricing-page {
  padding: 40px 0;
  min-height: 100vh;
  background: var(--bg-secondary);
}

.page-header {
  text-align: center;
  margin-bottom: 40px;
}

.page-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 12px;
}

.page-description {
  color: var(--text-secondary);
  font-size: 1.1rem;
  max-width: 600px;
  margin: 0 auto;
}

.billing-toggle {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  margin-bottom: 40px;
}

.billing-options {
  background: white;
  border-radius: 8px;
  padding: 4px;
  box-shadow: var(--shadow-sm);
}

.discount-badge {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.pricing-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 30px;
  margin-bottom: 60px;
}

.pricing-card {
  background: white;
  border-radius: 16px;
  box-shadow: var(--shadow-md);
  padding: 32px 24px;
  position: relative;
  transition: all 0.3s ease;
}

.pricing-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.pricing-card.popular {
  border: 2px solid var(--primary-color);
  transform: scale(1.05);
}

.pricing-card.current {
  border: 2px solid var(--success-color);
}

.popular-badge {
  position: absolute;
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
  background: var(--primary-color);
  color: white;
  padding: 6px 16px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 4px;
}

.plan-header {
  text-align: center;
  margin-bottom: 32px;
}

.plan-name {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 16px;
}

.plan-price {
  display: flex;
  align-items: baseline;
  justify-content: center;
  margin-bottom: 12px;
}

.currency {
  font-size: 1.25rem;
  color: var(--text-secondary);
}

.amount {
  font-size: 3rem;
  font-weight: 700;
  color: var(--text-primary);
}

.period {
  font-size: 1rem;
  color: var(--text-secondary);
  margin-left: 4px;
}

.plan-description {
  color: var(--text-secondary);
  font-size: 14px;
}

.plan-features {
  margin-bottom: 32px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;
}

.feature-item.disabled {
  opacity: 0.5;
}

.feature-icon {
  flex-shrink: 0;
}

.action-button {
  width: 100%;
  height: 48px;
  font-size: 16px;
  font-weight: 600;
}

.faq-section {
  margin-bottom: 60px;
}

.section-title {
  text-align: center;
  font-size: 2rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 32px;
}

.faq-collapse {
  max-width: 800px;
  margin: 0 auto;
}

.support-section {
  display: flex;
  justify-content: center;
}

.support-card {
  background: white;
  border-radius: 16px;
  box-shadow: var(--shadow-md);
  padding: 40px;
  text-align: center;
  max-width: 400px;
}

.support-card h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 12px;
}

.support-card p {
  color: var(--text-secondary);
  margin-bottom: 24px;
}

@media (max-width: 768px) {
  .pricing-cards {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .pricing-card.popular {
    transform: none;
  }
  
  .billing-toggle {
    flex-direction: column;
    gap: 12px;
  }
}
</style>
