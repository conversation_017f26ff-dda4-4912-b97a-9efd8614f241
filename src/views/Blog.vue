<template>
  <div class="blog-page">
    <div class="container">
      <!-- 页面头部 -->
      <div class="page-header">
        <h1 class="page-title">投资洞察</h1>
        <p class="page-description">专业的金融分析文章和市场洞察</p>
      </div>

      <!-- 分类筛选 -->
      <div class="categories-filter">
        <el-button
          v-for="category in categories"
          :key="category.id"
          :type="selectedCategory === category.id ? 'primary' : ''"
          @click="selectedCategory = category.id"
          class="category-btn"
        >
          {{ category.name }}
        </el-button>
      </div>

      <!-- 文章列表 -->
      <div class="articles-grid">
        <div
          v-for="article in filteredArticles"
          :key="article.id"
          class="article-card"
          @click="openArticle(article)"
        >
          <div class="article-image">
            <img :src="article.image" :alt="article.title" />
            <div class="article-category">{{ article.categoryName }}</div>
          </div>
          <div class="article-content">
            <h3 class="article-title">{{ article.title }}</h3>
            <p class="article-excerpt">{{ article.excerpt }}</p>
            <div class="article-meta">
              <div class="author-info">
                <el-avatar :size="24" :src="article.author.avatar" />
                <span class="author-name">{{ article.author.name }}</span>
              </div>
              <div class="article-stats">
                <span class="publish-date">{{ formatDate(article.publishDate) }}</span>
                <span class="read-time">{{ article.readTime }} 分钟阅读</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 加载更多 -->
      <div class="load-more" v-if="hasMore">
        <el-button @click="loadMore" :loading="loading">
          加载更多
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { blogAPI } from '@/api'

// 响应式数据
const loading = ref(false)
const selectedCategory = ref('')
const articles = ref([])
const categories = ref([])
const hasMore = ref(true)
const currentPage = ref(1)

// 计算属性
const filteredArticles = computed(() => {
  if (!selectedCategory.value) return articles.value
  return articles.value.filter(article => article.categoryId === selectedCategory.value)
})

// 方法
const fetchCategories = async () => {
  try {
    const response = await blogAPI.getCategories()
    categories.value = [
      { id: '', name: '全部' },
      ...(response.data || mockCategories)
    ]
  } catch (error) {
    categories.value = [
      { id: '', name: '全部' },
      ...mockCategories
    ]
  }
}

const fetchArticles = async (page = 1) => {
  try {
    loading.value = true
    const response = await blogAPI.getPosts(page, 12, selectedCategory.value)
    const newArticles = response.data?.articles || mockArticles
    
    if (page === 1) {
      articles.value = newArticles
    } else {
      articles.value.push(...newArticles)
    }
    
    hasMore.value = newArticles.length === 12
  } catch (error) {
    if (page === 1) {
      articles.value = mockArticles
    }
  } finally {
    loading.value = false
  }
}

const loadMore = () => {
  currentPage.value++
  fetchArticles(currentPage.value)
}

const openArticle = (article) => {
  // TODO: 打开文章详情页
  console.log('Open article:', article)
}

const formatDate = (date) => {
  return new Date(date).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

// 模拟数据
const mockCategories = [
  { id: 'market', name: '市场分析' },
  { id: 'stock', name: '个股研究' },
  { id: 'strategy', name: '投资策略' },
  { id: 'news', name: '财经新闻' }
]

const mockArticles = [
  {
    id: 1,
    title: '2024年科技股投资机会分析',
    excerpt: '随着AI技术的快速发展，科技股迎来新的投资机遇。本文深入分析了当前科技股的投资价值和风险...',
    image: 'https://via.placeholder.com/400x240',
    categoryId: 'stock',
    categoryName: '个股研究',
    author: {
      name: '张分析师',
      avatar: 'https://via.placeholder.com/40x40'
    },
    publishDate: new Date().toISOString(),
    readTime: 8
  },
  {
    id: 2,
    title: '美联储政策对全球市场的影响',
    excerpt: '美联储的货币政策调整对全球金融市场产生深远影响。我们来分析一下最新政策变化的市场含义...',
    image: 'https://via.placeholder.com/400x240',
    categoryId: 'market',
    categoryName: '市场分析',
    author: {
      name: '李专家',
      avatar: 'https://via.placeholder.com/40x40'
    },
    publishDate: new Date(Date.now() - 86400000).toISOString(),
    readTime: 12
  },
  {
    id: 3,
    title: '价值投资在当前市场环境下的应用',
    excerpt: '巴菲特的价值投资理念在当前复杂的市场环境下如何应用？本文为您详细解读价值投资的核心要点...',
    image: 'https://via.placeholder.com/400x240',
    categoryId: 'strategy',
    categoryName: '投资策略',
    author: {
      name: '王顾问',
      avatar: 'https://via.placeholder.com/40x40'
    },
    publishDate: new Date(Date.now() - 172800000).toISOString(),
    readTime: 15
  }
]

onMounted(() => {
  fetchCategories()
  fetchArticles()
})
</script>

<style scoped>
.blog-page {
  padding: 40px 0;
  min-height: 100vh;
  background: var(--bg-secondary);
}

.page-header {
  text-align: center;
  margin-bottom: 40px;
}

.page-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 12px;
}

.page-description {
  color: var(--text-secondary);
  font-size: 1.1rem;
}

.categories-filter {
  display: flex;
  justify-content: center;
  gap: 12px;
  margin-bottom: 40px;
  flex-wrap: wrap;
}

.category-btn {
  border-radius: 20px;
}

.articles-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 30px;
  margin-bottom: 40px;
}

.article-card {
  background: white;
  border-radius: 16px;
  box-shadow: var(--shadow-md);
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
}

.article-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.article-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.article-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.article-card:hover .article-image img {
  transform: scale(1.05);
}

.article-category {
  position: absolute;
  top: 12px;
  left: 12px;
  background: var(--primary-color);
  color: white;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.article-content {
  padding: 24px;
}

.article-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 12px;
  line-height: 1.4;
}

.article-excerpt {
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: 16px;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.article-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.author-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.author-name {
  font-size: 14px;
  color: var(--text-secondary);
  font-weight: 500;
}

.article-stats {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
  font-size: 12px;
  color: var(--text-muted);
}

.load-more {
  text-align: center;
}

@media (max-width: 768px) {
  .articles-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .categories-filter {
    justify-content: flex-start;
    overflow-x: auto;
    padding-bottom: 8px;
  }
  
  .category-btn {
    flex-shrink: 0;
  }
}
</style>
