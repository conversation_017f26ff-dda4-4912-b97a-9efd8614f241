<template>
  <div class="home-page">
    <!-- 英雄区域 -->
    <section class="hero-section">
      <div class="container">
        <div class="hero-content">
          <h1 class="hero-title">
            智能金融分析，
            <span class="text-gradient">AI 驱动投资决策</span>
          </h1>
          <p class="hero-description">
            通过强大的人工智能技术，为您提供专业的股票分析、市场洞察和投资建议
          </p>
          
          <!-- 搜索区域 -->
          <div class="search-section">
            <div class="search-container">
              <el-input
                v-model="searchSymbol"
                :placeholder="$t('home.search_placeholder')"
                size="large"
                class="search-input"
                @keyup.enter="handleAnalyze"
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
              <el-button
                type="primary"
                size="large"
                class="analyze-btn"
                @click="handleAnalyze"
                :loading="isAnalyzing"
              >
                {{ $t('home.analyze_btn') }}
              </el-button>
            </div>
            <div class="search-examples">
              <span class="examples-label">热门股票：</span>
              <el-tag
                v-for="example in stockExamples"
                :key="example"
                class="example-tag"
                @click="searchSymbol = example"
              >
                {{ example }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 主要内容区域 -->
    <section class="main-content-section">
      <div class="container">
        <div class="content-grid">
          <!-- 财经早报 -->
          <div class="content-card">
            <div class="card-header">
              <h2 class="section-title">
                <el-icon><Document /></el-icon>
                {{ $t('home.financial_news') }}
              </h2>
              <el-button text type="primary" @click="viewAllNews">
                查看全部
              </el-button>
            </div>
            <div class="card-body">
              <div v-if="newsLoading" class="loading-container">
                <el-skeleton :rows="3" animated />
              </div>
              <div v-else class="news-list">
                <div
                  v-for="news in financialNews"
                  :key="news.id"
                  class="news-item"
                  @click="openNews(news)"
                >
                  <div class="news-content">
                    <h3 class="news-title">{{ news.title }}</h3>
                    <p class="news-summary">{{ news.summary }}</p>
                    <div class="news-meta">
                      <span class="news-time">{{ formatTime(news.publishTime) }}</span>
                      <span class="news-source">{{ news.source }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 全球市场行情 -->
          <div class="content-card">
            <div class="card-header">
              <h2 class="section-title">
                <el-icon><TrendCharts /></el-icon>
                {{ $t('home.global_markets') }}
              </h2>
              <el-button text type="primary" @click="viewAllMarkets">
                查看详情
              </el-button>
            </div>
            <div class="card-body">
              <div v-if="marketsLoading" class="loading-container">
                <el-skeleton :rows="5" animated />
              </div>
              <div v-else class="markets-list">
                <div
                  v-for="market in globalMarkets"
                  :key="market.id"
                  class="market-item"
                >
                  <div class="market-info">
                    <div class="market-header">
                      <h3 class="market-name">{{ market.name }}</h3>
                      <div class="market-change" :class="market.changeClass">
                        <span class="change-value">{{ market.change }}</span>
                        <span class="change-percent">{{ market.changePercent }}</span>
                      </div>
                    </div>
                    <p class="market-analysis">{{ market.analysis }}</p>
                  </div>
                  <div class="market-chart">
                    <!-- <MiniChart :data="market.chartData" :trend="market.trend" /> -->
                    <div style="width: 60px; height: 30px; background: #f0f0f0; border-radius: 4px; display: flex; align-items: center; justify-content: center; font-size: 12px; color: #666;">
                      图表
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 特性展示 -->
    <section class="features-section">
      <div class="container">
        <h2 class="section-title text-center">为什么选择 SmartCompass</h2>
        <div class="features-grid">
          <div class="feature-card" v-for="feature in features" :key="feature.id">
            <div class="feature-icon">
              <el-icon size="48" :color="feature.color">
                <component :is="feature.icon" />
              </el-icon>
            </div>
            <h3 class="feature-title">{{ feature.title }}</h3>
            <p class="feature-description">{{ feature.description }}</p>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
// import MiniChart from '@/components/Charts/MiniChart.vue'
import {
  Search,
  Document,
  TrendCharts
} from '@element-plus/icons-vue'

const router = useRouter()

// 响应式数据
const searchSymbol = ref('')
const isAnalyzing = ref(false)
const newsLoading = ref(false)
const marketsLoading = ref(false)
const financialNews = ref([])
const globalMarkets = ref([])

// 静态数据
const stockExamples = ['AAPL', 'TSLA', '00700.HK', 'NVDA', 'MSFT']

const features = [
  {
    id: 1,
    icon: 'TrendCharts',
    title: 'AI 智能分析',
    description: '基于深度学习的股票分析模型，提供精准的投资建议',
    color: '#3b82f6'
  },
  {
    id: 2,
    icon: 'Document',
    title: '实时数据',
    description: '毫秒级数据更新，确保您获得最新的市场信息',
    color: '#10b981'
  },
  {
    id: 3,
    icon: 'Search',
    title: '风险控制',
    description: '智能风险评估系统，帮助您规避投资风险',
    color: '#f59e0b'
  },
  {
    id: 4,
    icon: 'TrendCharts',
    title: '专业团队',
    description: '由资深金融专家和AI工程师组成的专业团队',
    color: '#8b5cf6'
  }
]

// 方法
const handleAnalyze = () => {
  if (!searchSymbol.value.trim()) {
    ElMessage.warning('请输入股票代码')
    return
  }
  
  isAnalyzing.value = true
  
  // 模拟分析过程
  setTimeout(() => {
    isAnalyzing.value = false
    router.push(`/analysis/${searchSymbol.value.toUpperCase()}`)
  }, 1000)
}

const fetchFinancialNews = async () => {
  newsLoading.value = true
  // 模拟加载时间
  setTimeout(() => {
    financialNews.value = mockNews
    newsLoading.value = false
  }, 500)
}

const fetchGlobalMarkets = async () => {
  marketsLoading.value = true
  // 模拟加载时间
  setTimeout(() => {
    globalMarkets.value = mockMarkets
    marketsLoading.value = false
  }, 800)
}

const formatTime = (time) => {
  return new Date(time).toLocaleString('zh-CN', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const openNews = (news) => {
  window.open(news.url, '_blank')
}

const viewAllNews = () => {
  router.push('/blog')
}

const viewAllMarkets = () => {
  ElMessage.info('市场详情页面开发中...')
}

// 模拟数据
const mockNews = [
  {
    id: 1,
    title: '美联储加息预期推动美元走强，科技股承压',
    summary: '市场预期美联储将在下次会议中继续加息，推动美元指数上涨，科技股普遍下跌...',
    publishTime: new Date().toISOString(),
    source: '财经日报',
    url: '#'
  },
  {
    id: 2,
    title: '中概股集体反弹，阿里巴巴涨超5%',
    summary: '受益于政策利好消息，中概股今日集体反弹，阿里巴巴领涨...',
    publishTime: new Date(Date.now() - 3600000).toISOString(),
    source: '投资快报',
    url: '#'
  }
]

const mockMarkets = [
  {
    id: 1,
    name: '纳斯达克指数',
    change: '+125.43',
    changePercent: '+0.85%',
    changeClass: 'positive',
    analysis: '科技股反弹推动指数上涨，市场情绪有所改善',
    trend: 'up',
    chartData: [100, 102, 98, 105, 108, 106, 110]
  },
  {
    id: 2,
    name: '恒生指数',
    change: '-89.12',
    changePercent: '-0.42%',
    changeClass: 'negative',
    analysis: '地产股拖累指数下跌，但跌幅有所收窄',
    trend: 'down',
    chartData: [100, 98, 96, 99, 97, 95, 94]
  }
]

onMounted(() => {
  fetchFinancialNews()
  fetchGlobalMarkets()
})
</script>

<style scoped>
.home-page {
  min-height: 100vh;
}

.hero-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 80px 0;
  text-align: center;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 24px;
  line-height: 1.2;
}

.hero-description {
  font-size: 1.25rem;
  margin-bottom: 48px;
  opacity: 0.9;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.search-section {
  max-width: 600px;
  margin: 0 auto;
}

.search-container {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
}

.search-input {
  flex: 1;
}

.analyze-btn {
  min-width: 120px;
}

.search-examples {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
  justify-content: center;
}

.examples-label {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
}

.example-tag {
  cursor: pointer;
  transition: all 0.3s ease;
}

.example-tag:hover {
  transform: scale(1.05);
}

.main-content-section {
  padding: 80px 0;
}

.content-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
}

.content-card {
  background: white;
  border-radius: 16px;
  box-shadow: var(--shadow-lg);
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  border-bottom: 1px solid var(--border-color);
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
}

.card-body {
  padding: 24px;
}

.loading-container {
  padding: 20px 0;
}

.news-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.news-item {
  cursor: pointer;
  padding: 16px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.news-item:hover {
  background: var(--bg-secondary);
}

.news-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 8px;
  color: var(--text-primary);
}

.news-summary {
  color: var(--text-secondary);
  font-size: 14px;
  margin-bottom: 8px;
  line-height: 1.5;
}

.news-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: var(--text-muted);
}

.markets-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.market-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

.market-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.market-name {
  font-size: 16px;
  font-weight: 600;
  margin: 0;
}

.market-change {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  font-size: 14px;
  font-weight: 600;
}

.market-change.positive {
  color: var(--success-color);
}

.market-change.negative {
  color: var(--error-color);
}

.market-analysis {
  color: var(--text-secondary);
  font-size: 14px;
  margin: 0;
}

.market-chart {
  width: 100px;
  height: 60px;
}

.features-section {
  background: var(--bg-secondary);
  padding: 80px 0;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
  margin-top: 60px;
}

.feature-card {
  text-align: center;
  padding: 40px 20px;
  background: white;
  border-radius: 16px;
  box-shadow: var(--shadow-md);
  transition: all 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-lg);
}

.feature-icon {
  margin-bottom: 24px;
}

.feature-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 16px;
}

.feature-description {
  color: var(--text-secondary);
  line-height: 1.6;
}

@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }
  
  .content-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }
  
  .search-container {
    flex-direction: column;
  }
  
  .market-item {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }
}
</style>
