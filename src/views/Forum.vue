<template>
  <div class="forum-page">
    <div class="container">
      <!-- 页面头部 -->
      <div class="page-header">
        <div class="header-content">
          <h1 class="page-title">投资者社区</h1>
          <p class="page-description">与其他投资者交流心得，分享投资经验</p>
        </div>
        <el-button
          v-if="userStore.isLoggedIn"
          type="primary"
          @click="showCreatePost = true"
        >
          <el-icon><Edit /></el-icon>
          发布帖子
        </el-button>
      </div>

      <!-- 分类和筛选 -->
      <div class="forum-filters">
        <div class="categories">
          <el-button
            v-for="category in categories"
            :key="category.id"
            :type="selectedCategory === category.id ? 'primary' : ''"
            @click="selectedCategory = category.id"
            class="category-btn"
          >
            {{ category.name }}
          </el-button>
        </div>
        <div class="sort-options">
          <el-select v-model="sortBy" placeholder="排序方式" style="width: 120px">
            <el-option label="最新" value="latest" />
            <el-option label="最热" value="hot" />
            <el-option label="精华" value="featured" />
          </el-select>
        </div>
      </div>

      <!-- 帖子列表 -->
      <div class="posts-list">
        <div
          v-for="post in filteredPosts"
          :key="post.id"
          class="post-item"
          @click="openPost(post)"
        >
          <div class="post-avatar">
            <el-avatar :src="post.author.avatar" :size="48" />
          </div>
          <div class="post-content">
            <div class="post-header">
              <h3 class="post-title">{{ post.title }}</h3>
              <div class="post-tags">
                <el-tag
                  v-for="tag in post.tags"
                  :key="tag"
                  size="small"
                  class="post-tag"
                >
                  {{ tag }}
                </el-tag>
              </div>
            </div>
            <p class="post-excerpt">{{ post.excerpt }}</p>
            <div class="post-meta">
              <div class="author-info">
                <span class="author-name">{{ post.author.name }}</span>
                <span class="post-time">{{ formatTime(post.createdAt) }}</span>
              </div>
              <div class="post-stats">
                <span class="stat-item">
                  <el-icon><View /></el-icon>
                  {{ post.views }}
                </span>
                <span class="stat-item">
                  <el-icon><ChatDotRound /></el-icon>
                  {{ post.replies }}
                </span>
                <span class="stat-item">
                  <el-icon><Star /></el-icon>
                  {{ post.likes }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 加载更多 -->
      <div class="load-more" v-if="hasMore">
        <el-button @click="loadMore" :loading="loading">
          加载更多
        </el-button>
      </div>
    </div>

    <!-- 发布帖子弹窗 -->
    <el-dialog
      v-model="showCreatePost"
      title="发布新帖子"
      width="600px"
      :before-close="handleCloseCreatePost"
    >
      <el-form :model="newPost" label-width="80px">
        <el-form-item label="标题" required>
          <el-input
            v-model="newPost.title"
            placeholder="请输入帖子标题"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>
        
        <el-form-item label="分类" required>
          <el-select v-model="newPost.categoryId" placeholder="选择分类">
            <el-option
              v-for="category in categories.filter(c => c.id)"
              :key="category.id"
              :label="category.name"
              :value="category.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="标签">
          <el-input
            v-model="newPost.tags"
            placeholder="输入标签，用逗号分隔"
          />
        </el-form-item>

        <el-form-item label="内容" required>
          <el-input
            v-model="newPost.content"
            type="textarea"
            :rows="8"
            placeholder="请输入帖子内容"
            maxlength="2000"
            show-word-limit
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showCreatePost = false">取消</el-button>
        <el-button
          type="primary"
          @click="handleCreatePost"
          :loading="creating"
        >
          发布
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { forumAPI } from '@/api'
import { ElMessage } from 'element-plus'
import {
  Edit,
  View,
  ChatDotRound,
  Star
} from '@element-plus/icons-vue'

const userStore = useUserStore()

// 响应式数据
const loading = ref(false)
const creating = ref(false)
const selectedCategory = ref('')
const sortBy = ref('latest')
const posts = ref([])
const categories = ref([])
const hasMore = ref(true)
const currentPage = ref(1)
const showCreatePost = ref(false)

const newPost = ref({
  title: '',
  categoryId: '',
  tags: '',
  content: ''
})

// 计算属性
const filteredPosts = computed(() => {
  let filtered = posts.value
  
  if (selectedCategory.value) {
    filtered = filtered.filter(post => post.categoryId === selectedCategory.value)
  }
  
  // 根据排序方式排序
  switch (sortBy.value) {
    case 'hot':
      return filtered.sort((a, b) => (b.views + b.replies + b.likes) - (a.views + a.replies + a.likes))
    case 'featured':
      return filtered.filter(post => post.featured)
    default:
      return filtered.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
  }
})

// 方法
const fetchCategories = async () => {
  try {
    const response = await forumAPI.getCategories()
    categories.value = [
      { id: '', name: '全部' },
      ...(response.data || mockCategories)
    ]
  } catch (error) {
    categories.value = [
      { id: '', name: '全部' },
      ...mockCategories
    ]
  }
}

const fetchPosts = async (page = 1) => {
  try {
    loading.value = true
    const response = await forumAPI.getPosts(page, 20, selectedCategory.value)
    const newPosts = response.data?.posts || mockPosts
    
    if (page === 1) {
      posts.value = newPosts
    } else {
      posts.value.push(...newPosts)
    }
    
    hasMore.value = newPosts.length === 20
  } catch (error) {
    if (page === 1) {
      posts.value = mockPosts
    }
  } finally {
    loading.value = false
  }
}

const loadMore = () => {
  currentPage.value++
  fetchPosts(currentPage.value)
}

const openPost = (post) => {
  // TODO: 打开帖子详情页
  console.log('Open post:', post)
}

const handleCreatePost = async () => {
  if (!newPost.value.title || !newPost.value.content || !newPost.value.categoryId) {
    ElMessage.warning('请填写完整信息')
    return
  }

  try {
    creating.value = true
    const postData = {
      ...newPost.value,
      tags: newPost.value.tags.split(',').map(tag => tag.trim()).filter(Boolean)
    }
    
    const result = await forumAPI.createPost(postData)
    if (result.success) {
      ElMessage.success('发布成功')
      showCreatePost.value = false
      resetNewPost()
      fetchPosts() // 刷新帖子列表
    }
  } catch (error) {
    ElMessage.error('发布失败，请稍后重试')
  } finally {
    creating.value = false
  }
}

const handleCloseCreatePost = () => {
  if (newPost.value.title || newPost.value.content) {
    return confirm('确定要关闭吗？未保存的内容将丢失。')
  }
  return true
}

const resetNewPost = () => {
  newPost.value = {
    title: '',
    categoryId: '',
    tags: '',
    content: ''
  }
}

const formatTime = (time) => {
  const now = new Date()
  const postTime = new Date(time)
  const diff = now - postTime
  
  const minutes = Math.floor(diff / 60000)
  const hours = Math.floor(diff / 3600000)
  const days = Math.floor(diff / 86400000)
  
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  if (days < 7) return `${days}天前`
  
  return postTime.toLocaleDateString('zh-CN')
}

// 模拟数据
const mockCategories = [
  { id: 'discussion', name: '投资讨论' },
  { id: 'analysis', name: '股票分析' },
  { id: 'news', name: '市场动态' },
  { id: 'strategy', name: '投资策略' },
  { id: 'qa', name: '问答求助' }
]

const mockPosts = [
  {
    id: 1,
    title: '如何看待当前科技股的投资机会？',
    excerpt: '最近科技股波动较大，想听听大家的看法和投资策略...',
    categoryId: 'discussion',
    tags: ['科技股', '投资策略'],
    author: {
      name: '投资小白',
      avatar: 'https://via.placeholder.com/48x48'
    },
    createdAt: new Date().toISOString(),
    views: 1234,
    replies: 56,
    likes: 89,
    featured: false
  },
  {
    id: 2,
    title: '苹果公司Q4财报分析及后市展望',
    excerpt: '详细分析苹果最新财报数据，以及对未来股价走势的预测...',
    categoryId: 'analysis',
    tags: ['苹果', '财报分析', 'AAPL'],
    author: {
      name: '分析师老王',
      avatar: 'https://via.placeholder.com/48x48'
    },
    createdAt: new Date(Date.now() - 3600000).toISOString(),
    views: 2345,
    replies: 78,
    likes: 156,
    featured: true
  }
]

onMounted(() => {
  fetchCategories()
  fetchPosts()
})
</script>

<style scoped>
.forum-page {
  padding: 40px 0;
  min-height: 100vh;
  background: var(--bg-secondary);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
}

.header-content h1 {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 8px;
}

.header-content p {
  color: var(--text-secondary);
  font-size: 1.1rem;
}

.forum-filters {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  gap: 20px;
}

.categories {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.category-btn {
  border-radius: 20px;
}

.posts-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 40px;
}

.post-item {
  display: flex;
  gap: 16px;
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: var(--shadow-sm);
  cursor: pointer;
  transition: all 0.3s ease;
}

.post-item:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.post-content {
  flex: 1;
}

.post-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
  gap: 16px;
}

.post-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  line-height: 1.4;
}

.post-tags {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

.post-tag {
  font-size: 12px;
}

.post-excerpt {
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: 12px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.post-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.author-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.author-name {
  font-weight: 500;
  color: var(--text-primary);
}

.post-time {
  font-size: 14px;
  color: var(--text-muted);
}

.post-stats {
  display: flex;
  gap: 16px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  color: var(--text-muted);
}

.load-more {
  text-align: center;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .forum-filters {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }
  
  .categories {
    overflow-x: auto;
    padding-bottom: 8px;
  }
  
  .category-btn {
    flex-shrink: 0;
  }
  
  .post-item {
    flex-direction: column;
    gap: 12px;
  }
  
  .post-header {
    flex-direction: column;
    gap: 8px;
  }
  
  .post-stats {
    gap: 12px;
  }
}
</style>
