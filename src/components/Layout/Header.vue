<template>
  <header class="app-header">
    <div class="container">
      <div class="header-content">
        <!-- Logo 和品牌名 -->
        <div class="brand" @click="$router.push('/')">
          <div class="logo">
            <el-icon size="32" color="#3b82f6">
              <Compass />
            </el-icon>
          </div>
          <h1 class="brand-name">SmartCompass</h1>
        </div>

        <!-- 导航菜单 -->
        <nav class="nav-menu" v-if="!isMobile">
          <el-menu
            :default-active="activeMenu"
            mode="horizontal"
            :ellipsis="false"
            background-color="transparent"
            text-color="#4a5568"
            active-text-color="#3b82f6"
            @select="handleMenuSelect"
          >
            <el-menu-item index="/">{{ $t('nav.home') }}</el-menu-item>
            <el-menu-item index="/blog">{{ $t('nav.blog') }}</el-menu-item>
            <el-menu-item index="/pricing">{{ $t('nav.pricing') }}</el-menu-item>
            <el-menu-item index="/forum">{{ $t('nav.forum') }}</el-menu-item>
          </el-menu>
        </nav>

        <!-- 用户区域 -->
        <div class="user-area">
          <!-- 语言切换 -->
          <el-dropdown @command="changeLanguage" class="language-selector">
            <span class="language-btn">
              <el-icon><Globe /></el-icon>
              {{ currentLanguage }}
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="zh">中文</el-dropdown-item>
                <el-dropdown-item command="en">English</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>

          <!-- 用户信息 -->
          <div v-if="userStore.isLoggedIn" class="user-info">
            <el-dropdown @command="handleUserCommand">
              <div class="user-profile">
                <el-avatar :size="36" :src="userStore.userAvatar" />
                <span class="username">{{ userStore.userDisplayName }}</span>
                <span v-if="userStore.isVip" class="vip-badge">VIP</span>
              </div>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="profile">
                    <el-icon><User /></el-icon>
                    我的账户
                  </el-dropdown-item>
                  <el-dropdown-item command="settings">
                    <el-icon><Setting /></el-icon>
                    设置
                  </el-dropdown-item>
                  <el-dropdown-item divided command="logout">
                    <el-icon><SwitchButton /></el-icon>
                    退出登录
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>

          <!-- 游客状态 -->
          <div v-else class="guest-area">
            <span class="guest-text">{{ $t('home.guest') }}</span>
            <el-button type="primary" @click="$router.push('/auth')">
              {{ $t('nav.login') }}
            </el-button>
          </div>

          <!-- 移动端菜单按钮 -->
          <el-button
            v-if="isMobile"
            type="text"
            @click="showMobileMenu = true"
            class="mobile-menu-btn"
          >
            <el-icon size="24"><Menu /></el-icon>
          </el-button>
        </div>
      </div>
    </div>

    <!-- 移动端抽屉菜单 -->
    <el-drawer
      v-model="showMobileMenu"
      direction="rtl"
      size="280px"
      :with-header="false"
    >
      <div class="mobile-menu">
        <div class="mobile-brand">
          <el-icon size="28" color="#3b82f6"><Compass /></el-icon>
          <span class="brand-name">SmartCompass</span>
        </div>
        
        <el-menu
          :default-active="activeMenu"
          @select="handleMobileMenuSelect"
        >
          <el-menu-item index="/">
            <el-icon><House /></el-icon>
            <span>{{ $t('nav.home') }}</span>
          </el-menu-item>
          <el-menu-item index="/blog">
            <el-icon><Document /></el-icon>
            <span>{{ $t('nav.blog') }}</span>
          </el-menu-item>
          <el-menu-item index="/pricing">
            <el-icon><PriceTag /></el-icon>
            <span>{{ $t('nav.pricing') }}</span>
          </el-menu-item>
          <el-menu-item index="/forum">
            <el-icon><ChatDotRound /></el-icon>
            <span>{{ $t('nav.forum') }}</span>
          </el-menu-item>
          <el-menu-item v-if="userStore.isLoggedIn" index="/my">
            <el-icon><User /></el-icon>
            <span>{{ $t('nav.my') }}</span>
          </el-menu-item>
        </el-menu>
      </div>
    </el-drawer>
  </header>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'
import {
  Compass,
  Globe,
  User,
  Setting,
  SwitchButton,
  Menu,
  House,
  Document,
  PriceTag,
  ChatDotRound
} from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()
const { locale } = useI18n()
const userStore = useUserStore()

const showMobileMenu = ref(false)
const isMobile = ref(false)

const activeMenu = computed(() => route.path)
const currentLanguage = computed(() => locale.value === 'zh' ? '中文' : 'EN')

// 检测移动端
const checkMobile = () => {
  isMobile.value = window.innerWidth < 768
}

// 菜单选择处理
const handleMenuSelect = (index) => {
  router.push(index)
}

const handleMobileMenuSelect = (index) => {
  showMobileMenu.value = false
  router.push(index)
}

// 用户操作处理
const handleUserCommand = (command) => {
  switch (command) {
    case 'profile':
      router.push('/my')
      break
    case 'settings':
      // TODO: 打开设置弹窗
      ElMessage.info('设置功能开发中...')
      break
    case 'logout':
      userStore.logout()
      ElMessage.success('已退出登录')
      router.push('/')
      break
  }
}

// 语言切换
const changeLanguage = (lang) => {
  locale.value = lang
  localStorage.setItem('locale', lang)
  ElMessage.success(`已切换到${lang === 'zh' ? '中文' : 'English'}`)
}

onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})
</script>

<style scoped>
.app-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--border-color);
  position: sticky;
  top: 0;
  z-index: 1000;
  box-shadow: var(--shadow-sm);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 64px;
}

.brand {
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.brand:hover {
  transform: scale(1.02);
}

.logo {
  margin-right: 12px;
}

.brand-name {
  font-size: 24px;
  font-weight: 700;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
}

.nav-menu {
  flex: 1;
  display: flex;
  justify-content: center;
  margin: 0 40px;
}

.user-area {
  display: flex;
  align-items: center;
  gap: 16px;
}

.language-selector {
  cursor: pointer;
}

.language-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px 12px;
  border-radius: 6px;
  transition: all 0.3s ease;
  color: var(--text-secondary);
}

.language-btn:hover {
  background: var(--bg-secondary);
  color: var(--primary-color);
}

.user-profile {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.user-profile:hover {
  background: var(--bg-secondary);
}

.username {
  font-weight: 500;
  color: var(--text-primary);
}

.guest-area {
  display: flex;
  align-items: center;
  gap: 12px;
}

.guest-text {
  color: var(--text-secondary);
  font-size: 14px;
}

.mobile-menu-btn {
  padding: 8px;
}

.mobile-menu {
  padding: 20px;
}

.mobile-brand {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 0;
  border-bottom: 1px solid var(--border-color);
  margin-bottom: 20px;
}

.mobile-brand .brand-name {
  font-size: 20px;
  font-weight: 700;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

@media (max-width: 768px) {
  .header-content {
    height: 56px;
  }
  
  .brand-name {
    font-size: 20px;
  }
  
  .user-area {
    gap: 8px;
  }
  
  .guest-text {
    display: none;
  }
}
</style>
