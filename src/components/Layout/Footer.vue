<template>
  <footer class="app-footer">
    <div class="container">
      <div class="footer-content">
        <!-- 品牌信息 -->
        <div class="footer-section brand-section">
          <div class="brand">
            <el-icon size="28" color="#3b82f6">
              <Compass />
            </el-icon>
            <h3 class="brand-name">SmartCompass</h3>
          </div>
          <p class="brand-description">
            通过强大的 AI 技术为用户提供智能、深入的金融市场分析
          </p>
          <div class="social-links">
            <a href="#" class="social-link">
              <el-icon><Platform /></el-icon>
            </a>
            <a href="#" class="social-link">
              <el-icon><ChatDotRound /></el-icon>
            </a>
            <a href="#" class="social-link">
              <el-icon><Share /></el-icon>
            </a>
          </div>
        </div>

        <!-- 产品链接 -->
        <div class="footer-section">
          <h4 class="section-title">产品</h4>
          <ul class="link-list">
            <li><router-link to="/">股票分析</router-link></li>
            <li><router-link to="/pricing">订阅计划</router-link></li>
            <li><router-link to="/blog">投资洞察</router-link></li>
            <li><router-link to="/forum">社区论坛</router-link></li>
          </ul>
        </div>

        <!-- 支持链接 -->
        <div class="footer-section">
          <h4 class="section-title">支持</h4>
          <ul class="link-list">
            <li><a href="#">帮助中心</a></li>
            <li><a href="#">API 文档</a></li>
            <li><a href="#">联系我们</a></li>
            <li><a href="#">意见反馈</a></li>
          </ul>
        </div>

        <!-- 公司信息 -->
        <div class="footer-section">
          <h4 class="section-title">公司</h4>
          <ul class="link-list">
            <li><a href="#">关于我们</a></li>
            <li><a href="#">隐私政策</a></li>
            <li><a href="#">服务条款</a></li>
            <li><a href="#">免责声明</a></li>
          </ul>
        </div>
      </div>

      <!-- 版权信息 -->
      <div class="footer-bottom">
        <div class="copyright">
          <p>&copy; 2024 SmartCompass. All rights reserved.</p>
        </div>
        <div class="footer-links">
          <a href="#">隐私政策</a>
          <span class="separator">|</span>
          <a href="#">服务条款</a>
          <span class="separator">|</span>
          <a href="#">Cookie 政策</a>
        </div>
      </div>
    </div>
  </footer>
</template>

<script setup>
import { Compass, Platform, ChatDotRound, Share } from '@element-plus/icons-vue'
</script>

<style scoped>
.app-footer {
  background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
  color: #e2e8f0;
  margin-top: auto;
}

.footer-content {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: 40px;
  padding: 60px 0 40px;
}

.footer-section {
  display: flex;
  flex-direction: column;
}

.brand-section {
  max-width: 300px;
}

.brand {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.brand-name {
  font-size: 24px;
  font-weight: 700;
  background: linear-gradient(135deg, #60a5fa 0%, #a78bfa 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
}

.brand-description {
  color: #a0aec0;
  line-height: 1.6;
  margin-bottom: 24px;
}

.social-links {
  display: flex;
  gap: 12px;
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: #e2e8f0;
  transition: all 0.3s ease;
  text-decoration: none;
}

.social-link:hover {
  background: rgba(59, 130, 246, 0.2);
  color: #60a5fa;
  transform: translateY(-2px);
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #f7fafc;
  margin-bottom: 20px;
}

.link-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.link-list li {
  margin-bottom: 12px;
}

.link-list a {
  color: #a0aec0;
  text-decoration: none;
  transition: color 0.3s ease;
}

.link-list a:hover {
  color: #60a5fa;
}

.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 0;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.copyright p {
  color: #718096;
  margin: 0;
}

.footer-links {
  display: flex;
  align-items: center;
  gap: 8px;
}

.footer-links a {
  color: #718096;
  text-decoration: none;
  font-size: 14px;
  transition: color 0.3s ease;
}

.footer-links a:hover {
  color: #a0aec0;
}

.separator {
  color: #4a5568;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .footer-content {
    grid-template-columns: 1fr 1fr;
    gap: 30px;
  }
  
  .brand-section {
    grid-column: 1 / -1;
    max-width: none;
  }
}

@media (max-width: 768px) {
  .footer-content {
    grid-template-columns: 1fr;
    gap: 30px;
    padding: 40px 0 30px;
  }
  
  .footer-bottom {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
  
  .footer-links {
    flex-wrap: wrap;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .brand {
    justify-content: center;
    text-align: center;
  }
  
  .brand-description {
    text-align: center;
  }
  
  .social-links {
    justify-content: center;
  }
  
  .section-title {
    text-align: center;
  }
  
  .link-list {
    text-align: center;
  }
}
</style>
