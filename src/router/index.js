import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '@/stores/user'

// 路由组件懒加载
const Home = () => import('@/views/Home.vue')
const My = () => import('@/views/My.vue')
const Analysis = () => import('@/views/Analysis.vue')
const Auth = () => import('@/views/Auth.vue')
const Blog = () => import('@/views/Blog.vue')
const Pricing = () => import('@/views/Pricing.vue')
const Forum = () => import('@/views/Forum.vue')

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home,
    meta: {
      title: 'SmartCompass - 智能金融分析平台',
      requiresAuth: false
    }
  },
  {
    path: '/my',
    name: 'My',
    component: My,
    meta: {
      title: '我的账户 - SmartCompass',
      requiresAuth: true
    }
  },
  {
    path: '/analysis/:symbol?',
    name: 'Analysis',
    component: Analysis,
    props: true,
    meta: {
      title: '股票分析 - SmartCompass',
      requiresAuth: false
    }
  },
  {
    path: '/auth',
    name: 'Auth',
    component: Auth,
    meta: {
      title: '登录注册 - SmartCompass',
      requiresAuth: false
    }
  },
  {
    path: '/blog',
    name: 'Blog',
    component: Blog,
    meta: {
      title: '博客 - SmartCompass',
      requiresAuth: false
    }
  },
  {
    path: '/pricing',
    name: 'Pricing',
    component: Pricing,
    meta: {
      title: '定价 - SmartCompass',
      requiresAuth: false
    }
  },
  {
    path: '/forum',
    name: 'Forum',
    component: Forum,
    meta: {
      title: '论坛 - SmartCompass',
      requiresAuth: false
    }
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/'
  }
]

const router = createRouter({
  history: createWebHistory(process.env.BASE_URL),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const userStore = useUserStore()
  
  // 设置页面标题
  if (to.meta.title) {
    document.title = to.meta.title
  }
  
  // 检查是否需要登录
  if (to.meta.requiresAuth && !userStore.isLoggedIn) {
    next('/auth')
  } else {
    next()
  }
})

export default router
