# SmartCompass 项目启动指南

## 快速开始

### 1. 安装依赖

由于项目已经包含了所有必要的依赖配置，您需要先安装这些依赖：

```bash
# 使用 npm
npm install

# 或使用 yarn
yarn install

# 或使用 pnpm
pnpm install
```

### 2. 启动开发服务器

```bash
# 使用 npm
npm run serve

# 或使用 yarn
yarn serve

# 或使用 pnpm
pnpm serve
```

项目将在 `http://localhost:8080` 启动。

### 3. 项目结构说明

```
smart-composs-web/
├── public/                 # 静态资源
├── src/
│   ├── api/               # API 接口定义
│   ├── components/        # 可复用组件
│   │   ├── Layout/       # 布局组件
│   │   └── Charts/       # 图表组件
│   ├── router/           # 路由配置
│   ├── stores/           # Pinia 状态管理
│   ├── styles/           # 全局样式
│   ├── utils/            # 工具函数
│   ├── views/            # 页面组件
│   ├── App.vue           # 根组件
│   └── main.js           # 入口文件
├── .env.development      # 开发环境变量
├── .env.production       # 生产环境变量
└── package.json          # 项目配置
```

### 4. 主要功能页面

- **首页 (/)**: 股票搜索、财经早报、市场行情
- **股票分析 (/analysis/:symbol)**: AI 分析、技术指标、大师观点
- **我的账户 (/my)**: 用户信息、订阅管理、使用统计
- **登录注册 (/auth)**: 用户认证
- **博客 (/blog)**: 投资洞察文章
- **定价 (/pricing)**: 订阅计划
- **论坛 (/forum)**: 社区交流

### 5. 技术特性

- ✅ Vue 3 + Composition API
- ✅ Element Plus UI 组件库
- ✅ Vue Router 4 路由管理
- ✅ Pinia 状态管理
- ✅ Vue I18n 国际化
- ✅ Axios HTTP 客户端
- ✅ 响应式设计
- ✅ 现代化 CSS 样式

### 6. 开发注意事项

#### 6.1 API 接口
目前所有 API 调用都使用模拟数据，实际开发中需要：
1. 配置正确的后端 API 地址
2. 实现真实的 API 接口
3. 处理认证和权限

#### 6.2 图表功能
- 使用了 ECharts 进行数据可视化
- MiniChart 组件提供了基础的图表功能
- 可以根据需要扩展更多图表类型

#### 6.3 用户认证
- 实现了完整的登录注册流程
- 支持第三方登录（需要配置相应的 OAuth）
- 使用 JWT Token 进行身份验证

#### 6.4 国际化
- 支持中文和英文切换
- 可以在 `src/main.js` 中添加更多语言

### 7. 构建和部署

#### 开发环境
```bash
npm run serve
```

#### 生产构建
```bash
npm run build
```

#### 代码检查
```bash
npm run lint
```

### 8. 自定义配置

#### 修改 API 地址
编辑 `.env.development` 或 `.env.production` 文件中的 `VUE_APP_API_BASE_URL`

#### 修改主题色彩
编辑 `src/styles/global.css` 中的 CSS 变量

#### 添加新页面
1. 在 `src/views/` 创建新的 Vue 组件
2. 在 `src/router/index.js` 添加路由配置
3. 在 Header 组件中添加导航链接

### 9. 常见问题

#### Q: 如何添加新的 API 接口？
A: 在 `src/api/index.js` 中添加新的 API 方法，然后在组件中导入使用。

#### Q: 如何修改页面样式？
A: 每个组件都有自己的 `<style scoped>` 样式，全局样式在 `src/styles/global.css`。

#### Q: 如何添加新的状态管理？
A: 在 `src/stores/` 目录下创建新的 Pinia store 文件。

### 10. 技术支持

如果在开发过程中遇到问题，可以：
1. 查看浏览器控制台错误信息
2. 检查网络请求是否正常
3. 确认依赖是否正确安装
4. 参考 Vue 3 和 Element Plus 官方文档

---

祝您开发愉快！🚀
